# Change Log

All notable changes to the "Learning Docs" extension will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.0.1] - 2023-05-23

### Added

#### Phase 1: Core Infrastructure & Stable Change Detection
- Basic extension structure and configuration
- LoggerService for consistent logging
- ConfigService for managing extension settings
- WorkspaceService for workspace-related utilities
- StorageService for storing documentation records
- ChangeEventAggregatorService for detecting file changes
- ChangeProcessorService for processing change events
- Debouncing mechanism for external file changes

#### Phase 2: Robust File History & Accurate Raw Diff Generation
- FileHistoryService with cascading logic for retrieving previous versions:
  - In-memory cache (fastest)
  - Git repository (if available)
  - Persistent shadow cache (as fallback)
- DiffService for generating unified diffs between file versions
- Enhanced ChangeProcessorService to use these new services

#### Phase 3: Semantic Primitive Extraction
- SemanticAnalyzerService for analyzing code at a structural level
- Extraction of semantic primitives (functions, classes, methods, etc.)
- Detection of added, removed, and modified code elements
- Configuration options for semantic analysis

#### Phase 4: Contextual Analysis & AI-Powered Documentation
- ContextAnalyzerService for gathering contextual information:
  - Related files
  - References to changed elements
  - Related imports and dependencies
  - Commit history
  - Related test files
- AIDocumentationService for generating AI-powered documentation:
  - Summary of changes
  - Detailed explanation
  - Impact analysis
  - Suggestions
  - Learning resources
  - Code examples

#### Phase 5: User Interface & Refinements
- DocumentationPanel for displaying documentation
- DocumentationTreeProvider for navigating change history
- StatusBarItem for quick access
- Commands for interacting with documentation
- Tree view for exploring documentation records

### Changed
- Initial release, no changes yet

### Fixed
- Initial release, no fixes yet