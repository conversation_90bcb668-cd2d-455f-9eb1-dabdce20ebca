# FastAgent API Documentation

This document describes the API for the FastAgent backend service used by the Learning Docs extension for AI-powered documentation generation.

## Overview

The FastAgent is a backend service that analyzes code changes and generates natural language documentation. It provides a REST API that the Learning Docs extension can call to generate documentation for changes.

## API Endpoints

### Generate Documentation

**Endpoint**: `/analyze`

**Method**: `POST`

**Description**: Analyzes a code change and generates documentation.

**Request Body**:

```json
{
  "filePath": "path/to/file.ts",
  "eventType": "modify",
  "previousContent": "// Previous content of the file",
  "currentContent": "// Current content of the file",
  "rawDiff": "--- path/to/file.ts\n+++ path/to/file.ts\n@@ -1,1 +1,1 @@\n-// Previous content of the file\n+// Current content of the file",
  "semanticPrimitives": [
    {
      "type": "function",
      "name": "myFunction",
      "changeType": "added",
      "detail": "function myFunction(): void",
      "range": {
        "startLine": 1,
        "start<PERSON>haracter": 0,
        "endLine": 3,
        "endCharacter": 1
      }
    }
  ],
  "contextualInfo": {
    "relatedFiles": ["path/to/related-file.ts"],
    "references": [
      {
        "filePath": "path/to/reference.ts",
        "lineNumber": 10,
        "context": "import { myFunction } from './file';"
      }
    ],
    "relatedImports": ["lodash"],
    "relatedDependencies": ["lodash@4.17.21"],
    "commitHistory": [
      {
        "commitHash": "abc123",
        "author": "John Doe",
        "date": "2023-05-23T12:00:00Z",
        "message": "Initial commit"
      }
    ],
    "relatedTests": ["path/to/file.test.ts"]
  }
}
```

**Response Body**:

```json
{
  "summary": "Added a new function 'myFunction'",
  "explanation": "This change adds a new function called 'myFunction' that doesn't take any parameters and doesn't return a value.",
  "impact": "This function can now be called from other parts of the codebase.",
  "suggestions": [
    "Consider adding documentation comments to explain what the function does",
    "Add unit tests for the new function"
  ],
  "learningResources": [
    {
      "title": "TypeScript Functions",
      "url": "https://www.typescriptlang.org/docs/handbook/functions.html",
      "description": "Official documentation on TypeScript functions"
    }
  ],
  "codeExamples": [
    {
      "title": "Using myFunction",
      "code": "import { myFunction } from './file';\n\nmyFunction();",
      "explanation": "This example shows how to import and call the new function"
    }
  ]
}
```

**Status Codes**:

- `200 OK`: The request was successful
- `400 Bad Request`: The request was malformed
- `500 Internal Server Error`: An error occurred on the server

## Setting Up the FastAgent

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)

### Installation

1. Clone the FastAgent repository:
   ```bash
   git clone https://github.com/ALPHAbilal/fast-agent.git
   cd fast-agent
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the server:
   ```bash
   npm start
   ```

The server will start on port 8000 by default. You can configure the port by setting the `PORT` environment variable.

### Configuration

The FastAgent can be configured through environment variables:

- `PORT`: The port to listen on (default: `8000`)
- `LOG_LEVEL`: The log level (default: `info`)
- `AI_MODEL`: The AI model to use (default: `gpt-4`)
- `API_KEY`: The API key for the AI model

## Integrating with Learning Docs

To integrate the FastAgent with Learning Docs:

1. Start the FastAgent server
2. Open VS Code settings
3. Set `learningDocs.fastAgentUrl` to the URL of the FastAgent server (e.g., `http://localhost:8000/analyze`)

## API Examples

### Example 1: Function Addition

**Request**:

```json
{
  "filePath": "src/utils.ts",
  "eventType": "modify",
  "previousContent": "export function add(a: number, b: number): number {\n  return a + b;\n}\n",
  "currentContent": "export function add(a: number, b: number): number {\n  return a + b;\n}\n\nexport function subtract(a: number, b: number): number {\n  return a - b;\n}\n",
  "rawDiff": "--- src/utils.ts\n+++ src/utils.ts\n@@ -1,3 +1,7 @@\n export function add(a: number, b: number): number {\n   return a + b;\n }\n+\n+export function subtract(a: number, b: number): number {\n+  return a - b;\n+}\n",
  "semanticPrimitives": [
    {
      "type": "function",
      "name": "subtract",
      "changeType": "added",
      "detail": "function subtract(a: number, b: number): number",
      "range": {
        "startLine": 4,
        "startCharacter": 0,
        "endLine": 6,
        "endCharacter": 1
      }
    }
  ],
  "contextualInfo": {
    "relatedFiles": ["src/math.ts"],
    "references": [],
    "relatedImports": [],
    "relatedDependencies": [],
    "commitHistory": [],
    "relatedTests": ["src/utils.test.ts"]
  }
}
```

**Response**:

```json
{
  "summary": "Added a new subtract function",
  "explanation": "This change adds a new function called 'subtract' that takes two number parameters and returns their difference.",
  "impact": "This function can now be used for subtraction operations throughout the codebase, complementing the existing 'add' function.",
  "suggestions": [
    "Consider adding JSDoc comments to document the parameters and return value",
    "Add unit tests for the new subtract function",
    "Consider creating a comprehensive math utilities module"
  ],
  "learningResources": [
    {
      "title": "TypeScript Functions",
      "url": "https://www.typescriptlang.org/docs/handbook/functions.html",
      "description": "Official documentation on TypeScript functions"
    },
    {
      "title": "JavaScript Math",
      "url": "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math",
      "description": "MDN documentation on JavaScript math operations"
    }
  ],
  "codeExamples": [
    {
      "title": "Using the subtract function",
      "code": "import { subtract } from './utils';\n\nconst result = subtract(10, 5); // result = 5",
      "explanation": "This example shows how to import and use the new subtract function"
    }
  ]
}
```

## Error Handling

The FastAgent API returns appropriate HTTP status codes and error messages for different types of errors:

### Invalid Request

**Status Code**: `400 Bad Request`

**Response Body**:

```json
{
  "error": "Invalid request",
  "message": "The request body is missing required fields",
  "details": {
    "missingFields": ["filePath", "eventType"]
  }
}
```

### Server Error

**Status Code**: `500 Internal Server Error`

**Response Body**:

```json
{
  "error": "Server error",
  "message": "An error occurred while processing the request",
  "details": {
    "errorType": "AIModelError",
    "errorMessage": "Failed to connect to AI model"
  }
}
```

## Rate Limiting

The FastAgent API implements rate limiting to prevent abuse:

- Maximum of 60 requests per minute per IP address
- Maximum of 1000 requests per day per IP address

If the rate limit is exceeded, the API returns a `429 Too Many Requests` status code with a `Retry-After` header indicating when the client can make another request.
