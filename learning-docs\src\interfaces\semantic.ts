/**
 * Represents a range in a document for semantic primitives.
 */
export interface SemanticPrimitiveRange {
    startLine: number;
    startCharacter: number;
    endLine: number;
    endCharacter: number;
}

/**
 * Represents a semantic change primitive extracted from code analysis.
 */
export interface SemanticPrimitive {
    /**
     * The type of operation performed on the code element.
     */
    operation: 'add' | 'remove' | 'modify' | 'rename';
    
    /**
     * The type of code element that was changed.
     */
    elementType: 
        | 'function' 
        | 'method' 
        | 'class' 
        | 'interface' 
        | 'enum' 
        | 'variable'    // Top-level const/let/var
        | 'parameter'   // Function/method parameter
        | 'property'    // Class/interface property
        | 'unknown';    // Fallback for unrecognized elements
    
    /**
     * The name of the code element.
     */
    elementName: string;
    
    /**
     * The previous name of the element (for 'rename' operations).
     */
    oldElementName?: string;
    
    /**
     * The signature or details of the element (e.g., function parameters, return type).
     */
    signature?: string;
    
    /**
     * The location of the element in the document.
     * For 'add'/'modify' operations, this is the location in the current document.
     * For 'remove' operations, this is the location in the previous document.
     */
    range: SemanticPrimitiveRange;
    
    /**
     * Information about the parent element (for nested elements).
     */
    parentElement?: {
        name: string;
        type: SemanticPrimitive['elementType'];
    };
    
    /**
     * Accessibility modifier (if available from LSP).
     */
    accessibility?: 'public' | 'private' | 'protected';
}

/**
 * Result of semantic analysis containing extracted primitives.
 */
export interface SemanticAnalysisResult {
    /**
     * Array of semantic primitives found in the analysis.
     */
    primitives: SemanticPrimitive[];
    
    /**
     * The language ID that was analyzed.
     */
    languageId: string;
    
    /**
     * Confidence score for the analysis (0-1).
     * Lower scores might indicate LSP issues or complex changes.
     */
    confidence?: number;
    
    /**
     * High-level summary of changes.
     */
    summary?: {
        added: number;
        removed: number;
        modified: number;
        renamed: number;
    };
}

/**
 * Configuration for semantic analysis.
 */
export interface SemanticAnalysisConfig {
    /**
     * Languages for which semantic analysis is enabled.
     */
    enabledLanguages: string[];
    
    /**
     * Skip semantic analysis if text similarity is above this threshold (0-1).
     */
    skipIfTextSimilarityAbove: number;
    
    /**
     * Maximum depth for analyzing nested symbols.
     */
    maxNestingDepth: number;
    
    /**
     * Whether to attempt rename detection using heuristics.
     */
    enableRenameDetection: boolean;
}
