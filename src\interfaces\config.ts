/**
 * Interface for configuration service.
 */
export interface IConfigService {
    /**
     * Get a configuration setting by key.
     * @param key The configuration key.
     * @returns The configuration value, or undefined if not found.
     */
    getSetting<T>(key: string): T | undefined;
    
    /**
     * Check if a feature is enabled.
     * @param featureKey The feature key to check.
     * @returns True if the feature is enabled, false otherwise.
     */
    isFeatureEnabled(featureKey: string): boolean;
}
