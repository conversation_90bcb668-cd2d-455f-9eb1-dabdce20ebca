import * as vscode from 'vscode';
import { ILoggerService } from '../interfaces/logger';

/**
 * Status bar item for quick access to the extension.
 */
export class StatusBarItem {
    private readonly _statusBarItem: vscode.StatusBarItem;
    
    constructor(
        private readonly loggerService: ILoggerService
    ) {
        // Create the status bar item
        this._statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this._statusBarItem.text = '$(book) Learning Docs';
        this._statusBarItem.tooltip = 'Show Learning Documentation';
        this._statusBarItem.command = 'learningDocs.showDocumentationPanel';
        
        // Show the status bar item
        this._statusBarItem.show();
    }
    
    /**
     * Dispose of the status bar item.
     */
    public dispose(): void {
        this._statusBarItem.dispose();
    }
}
