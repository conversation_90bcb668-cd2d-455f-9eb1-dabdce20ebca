# Learning Docs User Guide

This guide provides detailed instructions for using the Learning Docs extension for VS Code.

## Table of Contents

- [Installation](#installation)
- [Getting Started](#getting-started)
- [Viewing Documentation](#viewing-documentation)
- [Understanding Documentation](#understanding-documentation)
- [Configuration](#configuration)
- [Troubleshooting](#troubleshooting)
- [FAQ](#faq)

## Installation

### From VS Code Marketplace

1. Open VS Code
2. Go to the Extensions view (Ctrl+Shift+X)
3. Search for "Learning Docs"
4. Click Install

### From VSIX File

1. Download the latest `.vsix` file from the [releases page](https://github.com/ALPHAbilal/one_one/releases)
2. Open VS Code
3. Go to the Extensions view (Ctrl+Shift+X)
4. Click the "..." menu in the top-right corner
5. Select "Install from VSIX..."
6. Choose the downloaded file

## Getting Started

After installing the extension, it will automatically start monitoring your workspace for changes. No additional setup is required.

### Verifying Installation

To verify that the extension is working:

1. Open the Command Palette (Ctrl+Shift+P)
2. Type "Learning Docs: Show Documentation Panel"
3. Press Enter
4. The documentation panel should open with a welcome message

### Making Your First Change

To see the extension in action:

1. Open or create a file in your workspace
2. Make some changes to the file
3. Save the file
4. Open the Documentation Panel to see the documentation for your change

## Viewing Documentation

There are several ways to access documentation:

### Status Bar

The extension adds a "Learning Docs" item to the status bar. Click on it to open the Documentation Panel.

### Explorer View

The extension adds a "Learning Documentation" view to the Explorer sidebar. This view shows a tree of files and changes:

- Files are shown at the root level
- Changes for each file are shown as children
- Click on a change to view its documentation

### Command Palette

You can access the extension through the Command Palette (Ctrl+Shift+P):

- "Learning Docs: Show Documentation Panel" - Opens the Documentation Panel
- "Learning Docs: Show Documentation" - Shows documentation for the current file
- "Learning Docs: Refresh Documentation" - Refreshes the Documentation Explorer view

### Context Menu

Right-click on a file in the editor and select "Learning Docs: Show Documentation" to view documentation for that file.

## Understanding Documentation

The Documentation Panel shows detailed information about changes, organized into several sections:

### AI Analysis

This section provides AI-generated documentation for the change, including:

- **Summary**: A brief summary of what the change does
- **Explanation**: A detailed explanation of the change
- **Impact**: The potential impact of the change on the codebase
- **Suggestions**: Suggestions for improvements or potential issues
- **Learning Resources**: Resources related to the concepts in the change
- **Code Examples**: Examples that demonstrate the concepts

### Semantic Changes

This section shows the structural changes to the code, including:

- Added, removed, or modified functions
- Added, removed, or modified classes and methods
- Added, removed, or modified properties and variables
- Changes to imports and exports

Each semantic primitive is color-coded:
- Green: Added
- Red: Removed
- Orange: Modified

### Diff

This section shows a line-by-line diff between the previous and current versions of the file. Lines are color-coded:

- Green: Added lines
- Red: Removed lines

### Contextual Information

This section provides contextual information about the change, including:

- **Related Files**: Files that might be affected by the change
- **References**: References to the changed elements in other files
- **Related Imports**: Imports related to the change
- **Related Dependencies**: Dependencies related to the change
- **Commit History**: Git commit history for the file
- **Related Tests**: Test files related to the change

## Configuration

Learning Docs can be configured through VS Code settings:

1. Open VS Code settings (File > Preferences > Settings)
2. Search for "Learning Docs"
3. Adjust the settings as needed

### General Settings

- `learningDocs.enable`: Enable/disable the Learning Docs extension (default: `true`)

### Feature Settings

- `learningDocs.enable.semanticAnalysis`: Enable/disable semantic analysis of code changes (default: `true`)
- `learningDocs.enable.contextualAnalysis`: Enable/disable contextual analysis of code changes (default: `true`)
- `learningDocs.enable.aiDocumentation`: Enable/disable AI-powered documentation generation (default: `true`)

### File Settings

- `learningDocs.ignoredFilesPatterns`: Patterns for files to ignore (default: `["**/node_modules/**", "**/.git/**"]`)
- `learningDocs.semanticAnalysis.enabledExtensions`: File extensions for which semantic analysis is enabled (default: `[".ts", ".js", ".tsx", ".jsx", ".py", ".java", ".cs", ".go", ".rb", ".php"]`)

### AI Settings

- `learningDocs.fastAgentUrl`: URL for the FastAgent backend (default: `"http://localhost:8000/analyze"`)

## Troubleshooting

### No Documentation Generated

If no documentation is generated for your changes:

1. Check if the file is being ignored by the extension
   - Open VS Code settings and check `learningDocs.ignoredFilesPatterns`
   - Make sure your file doesn't match any of the patterns

2. Check if the extension is enabled
   - Open VS Code settings and check `learningDocs.enable`
   - Make sure it's set to `true`

3. Check the Output panel
   - Open the Output panel (View > Output)
   - Select "Learning Docs" from the dropdown
   - Look for any error messages

### AI Documentation Not Working

If AI documentation is not being generated:

1. Check if AI documentation is enabled
   - Open VS Code settings and check `learningDocs.enable.aiDocumentation`
   - Make sure it's set to `true`

2. Check the FastAgent URL
   - Open VS Code settings and check `learningDocs.fastAgentUrl`
   - Make sure it's set to the correct URL

3. Check if the FastAgent is running
   - Try accessing the FastAgent URL in a browser
   - Make sure the FastAgent is running and accessible

### Semantic Analysis Not Working

If semantic analysis is not working:

1. Check if semantic analysis is enabled
   - Open VS Code settings and check `learningDocs.enable.semanticAnalysis`
   - Make sure it's set to `true`

2. Check if the file extension is supported
   - Open VS Code settings and check `learningDocs.semanticAnalysis.enabledExtensions`
   - Make sure your file's extension is in the list

## FAQ

### Q: Does Learning Docs work with all programming languages?

A: Learning Docs can detect changes in any file, but semantic analysis is only available for certain file types. By default, these include TypeScript, JavaScript, Python, Java, C#, Go, Ruby, and PHP. You can add more file extensions in the settings.

### Q: Does Learning Docs require an internet connection?

A: Learning Docs can work offline, but AI documentation generation requires the FastAgent backend, which may need an internet connection depending on your setup.

### Q: How does Learning Docs handle large files?

A: Learning Docs is designed to handle files of any size, but performance may be affected for very large files. If you experience performance issues, try excluding large files using the `learningDocs.ignoredFilesPatterns` setting.

### Q: Can I share documentation with my team?

A: Currently, documentation is stored locally and cannot be shared directly. However, you can export documentation by copying it from the Documentation Panel.

### Q: How can I contribute to Learning Docs?

A: Contributions are welcome! Please see the [CONTRIBUTING.md](../CONTRIBUTING.md) file for details.
