"""
Test file for semantic analysis testing.
This file will be used to test various semantic change scenarios.
"""

class Calculator:
    """A simple calculator class for testing."""

    def __init__(self):
        self.result = 0

    def add(self, x: int, y: int) -> int:
        """Add two numbers."""
        return x + y

    def subtract(self, x: int, y: int) -> int:
        """Subtract two numbers."""
        return x - y

def multiply(a: int, b: int) -> int:
    """Multiply two numbers."""
    return a * b

def divide(a: float, b: float) -> float:
    """Divide two numbers."""
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b

def simple_function() -> str:
    """A simple function for testing deletion."""
    return "Hello, World!"

# Global variable for testing
CONSTANT_VALUE = 42
