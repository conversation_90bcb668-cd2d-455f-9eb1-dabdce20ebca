/**
 * Interface for workspace initialization service.
 */
export interface IWorkspaceInitializerService {
    /**
     * Initialize the workspace on extension activation.
     * This method determines if this is a first run or resuming session,
     * and performs initial workspace scan if needed.
     * 
     * @returns A promise that resolves when initialization is complete.
     */
    initializeWorkspace(): Promise<void>;
    
    /**
     * Check if this is the first run in the current workspace.
     * 
     * @returns A promise that resolves to true if this is a first run, false otherwise.
     */
    isFirstRunInWorkspace(): Promise<boolean>;
    
    /**
     * Perform an initial workspace scan to baseline all existing files.
     * This creates baseline records and populates the file history cache.
     * 
     * @returns A promise that resolves when the scan is complete.
     */
    performInitialWorkspaceScan(): Promise<void>;
    
    /**
     * Mark the workspace as successfully initialized.
     * This prevents future initial scans for this workspace.
     * 
     * @returns A promise that resolves when the marker is set.
     */
    markWorkspaceAsInitialized(): Promise<void>;
}
