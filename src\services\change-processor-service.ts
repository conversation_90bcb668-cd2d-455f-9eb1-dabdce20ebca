import * as vscode from 'vscode';
import { IChangeProcessorService } from '../interfaces/change-processor';
import { IChangeEventAggregatorService } from '../interfaces/change-event-aggregator';
import { ILoggerService } from '../interfaces/logger';
import { IStorageService } from '../interfaces/storage-service';
import { IFileHistoryService } from '../interfaces/file-history';
import { IDiffService } from '../interfaces/diff';
import { ISemanticAnalyzerService } from '../interfaces/semantic-analyzer';
import { IContextAnalyzerService } from '../interfaces/context-analyzer';
import { IAIDocumentationService } from '../interfaces/ai-documentation';
import { IConfigService } from '../interfaces/config';
import { ChangeDescriptor } from '../interfaces/change';
import { ChangeDocumentationRecord } from '../interfaces/storage';

/**
 * Implementation of the change processor service.
 */
export class ChangeProcessorService implements IChangeProcessorService {
    private disposables: vscode.Disposable[] = [];

    constructor(
        private readonly loggerService: ILoggerService,
        private readonly changeEventAggregator: IChangeEventAggregatorService,
        private readonly storageService: IStorageService,
        private readonly fileHistoryService?: IFileHistoryService,
        private readonly diffService?: IDiffService,
        private readonly semanticAnalyzerService?: ISemanticAnalyzerService,
        private readonly contextAnalyzerService?: IContextAnalyzerService,
        private readonly aiDocumentationService?: IAIDocumentationService,
        private readonly configService?: IConfigService
    ) {}

    /**
     * Start processing changes.
     */
    public start(): void {
        this.loggerService.info('Starting change processor');

        // Start the change event aggregator
        this.changeEventAggregator.startListening();

        // Subscribe to stable change events
        this.disposables.push(
            this.changeEventAggregator.onDidStableChange(this.processChange.bind(this))
        );
    }

    /**
     * Stop processing changes.
     */
    public stop(): void {
        this.loggerService.info('Stopping change processor');

        // Stop the change event aggregator
        this.changeEventAggregator.stopListening();

        // Dispose all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }

    /**
     * Process a change event.
     * @param change The change to process.
     */
    private async processChange(change: ChangeDescriptor): Promise<void> {
        this.loggerService.info(`Processing change: ${change.eventType} - ${change.filePath}`);

        try {
            // Create a documentation record from the change
            const record: ChangeDocumentationRecord = {
                ...change
            };

            // For 'modify' events, get the previous content
            if (change.eventType === 'modify' && this.fileHistoryService) {
                record.previousContent = await this.fileHistoryService.getPreviousVersion(change.filePath);
            }

            // Generate diff if we have both previous and current content
            if (this.diffService &&
                (change.eventType === 'modify' || change.eventType === 'create') &&
                change.currentContent) {

                const previousContent = record.previousContent || '';
                const currentContent = change.currentContent;

                // Generate unified diff
                record.rawDiff = await this.diffService.generateUnifiedDiff(
                    previousContent,
                    currentContent,
                    change.filePath,
                    change.filePath
                );
            }

            // Perform semantic analysis if enabled
            if (this.semanticAnalyzerService &&
                this.configService?.isFeatureEnabled('semanticAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create') &&
                change.currentContent) {

                try {
                    const semanticAnalysis = await this.semanticAnalyzerService.getSemanticAnalysis(
                        change.filePath,
                        record.previousContent,
                        change.currentContent
                    );

                    if (semanticAnalysis) {
                        record.semanticPrimitives = semanticAnalysis.primitives;
                        this.loggerService.info(`Semantic analysis completed for ${change.filePath}: ${semanticAnalysis.primitives.length} primitives found`);
                    }
                } catch (error) {
                    this.loggerService.error(`Semantic analysis failed: ${error}`);
                }
            }

            // Perform contextual analysis if enabled
            if (this.contextAnalyzerService &&
                this.configService?.isFeatureEnabled('contextualAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create')) {

                try {
                    const contextualInfo = await this.contextAnalyzerService.getContextualInfo(
                        change.filePath,
                        record.semanticPrimitives
                    );

                    if (contextualInfo) {
                        record.contextualInfo = contextualInfo;
                        this.loggerService.info(`Contextual analysis completed for ${change.filePath}`);
                    }
                } catch (error) {
                    this.loggerService.error(`Contextual analysis failed: ${error}`);
                }
            }

            // Generate AI documentation if enabled
            if (this.aiDocumentationService &&
                this.configService?.isFeatureEnabled('aiDocumentation') &&
                (change.eventType === 'modify' || change.eventType === 'create')) {

                try {
                    // We need to save the record first to ensure it has all the data from previous steps
                    const tempRecord = { ...record };

                    const aiAnalysis = await this.aiDocumentationService.generateDocumentation(tempRecord);

                    if (aiAnalysis) {
                        record.aiAnalysis = aiAnalysis;
                        this.loggerService.info(`AI documentation generated for ${change.filePath}`);
                    }
                } catch (error) {
                    this.loggerService.error(`AI documentation generation failed: ${error}`);
                }
            }

            // Save the record
            await this.storageService.saveDocumentationRecord(record);

            // Update the file history cache with the current content
            if (this.fileHistoryService &&
                (change.eventType === 'modify' || change.eventType === 'create') &&
                change.currentContent) {
                await this.fileHistoryService.updateCache(change.filePath, change.currentContent);
            }

            this.loggerService.info(`Successfully processed change: ${change.id}`);
        } catch (error) {
            this.loggerService.error(`Failed to process change: ${error}`);
        }
    }

    /**
     * Dispose of resources.
     */
    public dispose(): void {
        this.stop();
    }
}
