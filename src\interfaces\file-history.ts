/**
 * Interface for file history service.
 */
export interface IFileHistoryService {
    /**
     * Get the previous version of a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the previous content, or undefined if not found.
     */
    getPreviousVersion(filePath: string): Promise<string | undefined>;
    
    /**
     * Update the cache with the current content of a file.
     * This should be called after a change is processed to store the current content
     * as the "previous version" for the next change.
     * 
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    updateCache(filePath: string, content: string): Promise<void>;
}
