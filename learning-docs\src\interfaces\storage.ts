import { ChangeDescriptor } from './change';
import { SemanticPrimitive } from './semantic-analyzer';
import { ContextualInfo } from './context-analyzer';
import { AIAnalysis } from './ai-documentation';

/**
 * Represents a stored documentation record for a change.
 */
export interface ChangeDocumentationRecord extends ChangeDescriptor {
    /**
     * Raw diff between previous and current content.
     */
    rawDiff?: string;

    /**
     * Extracted semantic primitives.
     */
    semanticPrimitives?: SemanticPrimitive[];

    /**
     * Contextual information about the change.
     */
    contextualInfo?: ContextualInfo;

    /**
     * AI analysis of the change.
     */
    aiAnalysis?: AIAnalysis;
}
