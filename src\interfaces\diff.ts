/**
 * Interface for diff service.
 */
export interface IDiffService {
    /**
     * Generate a unified diff between two versions of a file.
     * 
     * @param previousContent The previous content of the file.
     * @param currentContent The current content of the file.
     * @param oldFilePath The path to use in the diff for the old file.
     * @param newFilePath The path to use in the diff for the new file.
     * @returns A promise that resolves to the unified diff.
     */
    generateUnifiedDiff(
        previousContent: string,
        currentContent: string,
        oldFilePath: string,
        newFilePath: string
    ): Promise<string>;
}
