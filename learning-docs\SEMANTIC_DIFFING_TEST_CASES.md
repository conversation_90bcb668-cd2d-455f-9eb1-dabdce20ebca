# Semantic Diffing Test Cases 🧪

## Overview

Now that semantic analysis is working, we need to test and debug the `diffSymbolTrees` function to ensure it correctly identifies semantic changes. I've added comprehensive logging to trace exactly what's happening during symbol comparison.

## Enhanced Logging Added

The `diffSymbolTrees` method now logs:
- 📊 **Symbol counts** (old vs new)
- 📋 **Detailed symbol information** (name, kind, detail/signature)
- 🔧 **Flattening process** (how symbols are converted to keys)
- 🔑 **Flattened keys** (exact keys used for comparison)
- ➕➖✏️ **Change detection** (add/remove/modify operations)
- 🎯 **Final primitives** (what semantic changes were detected)

## Test Cases to Execute

### **Test Case 1: Add a New Function**

#### **Step 1: Create Baseline**
Create `test.ts`:
```typescript
class TestClass {
    existingMethod(param1: string) {
        console.log("hello", param1);
    }
}

function oldFunction() {
    return 1;
}
```
**Save this file** (creates baseline record)

#### **Step 2: Add New Function**
Modify `test.ts`:
```typescript
class TestClass {
    existingMethod(param1: string) {
        console.log("hello", param1);
    }
}

function oldFunction() {
    return 1;
}

function newFunction() {  // <-- ADDED
    return 2;
}
```
**Save again** and check logs

#### **Expected Result:**
- Should detect 1 ADD operation for `newFunction`
- Tree view should show: `➕ Function: 'newFunction'`

### **Test Case 2: Remove a Function**

#### **Step 1: Start with Multiple Functions**
Create `test2.ts`:
```typescript
function keepThis() {
    return "keep";
}

function removeThis() {  // <-- Will be removed
    return "remove";
}
```
**Save this file**

#### **Step 2: Remove Function**
Modify `test2.ts`:
```typescript
function keepThis() {
    return "keep";
}
// removeThis function deleted
```
**Save again** and check logs

#### **Expected Result:**
- Should detect 1 REMOVE operation for `removeThis`
- Tree view should show: `➖ Function: 'removeThis'`

### **Test Case 3: Modify Function Signature**

#### **Step 1: Create Function**
Create `test3.ts`:
```typescript
class Calculator {
    add(a: number, b: number) {
        return a + b;
    }
}
```
**Save this file**

#### **Step 2: Modify Signature**
Modify `test3.ts`:
```typescript
class Calculator {
    add(a: number, b: number, c: number) {  // <-- Added parameter
        return a + b + c;
    }
}
```
**Save again** and check logs

#### **Expected Result:**
- Should detect 1 MODIFY operation for `add` method
- Tree view should show: `✏️ Method: 'add'`

### **Test Case 4: Add Method to Class**

#### **Step 1: Create Class**
Create `test4.ts`:
```typescript
class UserService {
    getUser(id: string) {
        return { id, name: "User" };
    }
}
```
**Save this file**

#### **Step 2: Add Method**
Modify `test4.ts`:
```typescript
class UserService {
    getUser(id: string) {
        return { id, name: "User" };
    }
    
    createUser(data: any) {  // <-- ADDED
        return { ...data, id: "new" };
    }
}
```
**Save again** and check logs

#### **Expected Result:**
- Should detect 1 ADD operation for `createUser` method
- Tree view should show: `➕ Method: 'createUser'`

## Debug Log Analysis

### **What to Look For:**

#### **1. Symbol Extraction Success:**
```
[DIFF_SYMBOLS] 📋 OLD SYMBOLS:
  1. TestClass (Kind: 5, Detail: 'none')
    1. existingMethod (Kind: 6, Detail: 'existingMethod(param1: string): void')
  2. oldFunction (Kind: 12, Detail: 'oldFunction(): number')

[DIFF_SYMBOLS] 📋 NEW SYMBOLS:
  1. TestClass (Kind: 5, Detail: 'none')
    1. existingMethod (Kind: 6, Detail: 'existingMethod(param1: string): void')
  2. oldFunction (Kind: 12, Detail: 'oldFunction(): number')
  3. newFunction (Kind: 12, Detail: 'newFunction(): number')
```

#### **2. Flattening Process:**
```
[DIFF_SYMBOLS] 🔑 Previous keys: [TestClass:5, TestClass.existingMethod:6, oldFunction:12]
[DIFF_SYMBOLS] 🔑 Current keys: [TestClass:5, TestClass.existingMethod:6, oldFunction:12, newFunction:12]
```

#### **3. Change Detection:**
```
[DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[DIFF_SYMBOLS] ✅ FOUND ADD: 'newFunction:12' -> newFunction (Kind: 12)

[DIFF_SYMBOLS] 🎯 Generated 1 primitives:
  1. ADD function: 'newFunction' (newFunction(): number)
```

### **Common Issues to Debug:**

#### **Issue 1: No Symbols Detected**
```
[DIFF_SYMBOLS] 📋 OLD SYMBOLS:
[DIFF_SYMBOLS] 📋 NEW SYMBOLS:
```
**Cause:** LSP not working for the language or syntax errors

#### **Issue 2: Symbols Detected but No Changes**
```
[DIFF_SYMBOLS] 🔑 Previous keys: [TestClass:5]
[DIFF_SYMBOLS] 🔑 Current keys: [TestClass:5]
[DIFF_SYMBOLS] 🎯 Generated 0 primitives:
```
**Cause:** Keys are identical, no actual structural changes

#### **Issue 3: Wrong Change Type**
```
[DIFF_SYMBOLS] ✅ FOUND REMOVE: 'oldFunction:12'
[DIFF_SYMBOLS] ✅ FOUND ADD: 'newFunction:12'
```
**Should be:** MODIFY if it's the same function with different signature

## Testing Instructions

### **1. Reload Extension**
- `Ctrl+Shift+P` → `"Developer: Reload Window"`

### **2. Open Output Panel**
- `View > Output > Learning Docs`

### **3. Execute Test Cases**
- Follow each test case step-by-step
- **Save files** to trigger semantic analysis
- **Check logs** after each save

### **4. Verify Tree View**
- Check Learning Docs tree view
- Expand documentation records
- Verify semantic changes are displayed

### **5. Share Results**
For each test case, share:
- **Complete debug logs** from `[DIFF_SYMBOLS]` sections
- **Tree view screenshots** showing semantic changes
- **Any unexpected behavior**

## Expected Success Pattern

For a successful ADD operation:
```
[DIFF_SYMBOLS] 📊 Old symbols count: 2, New symbols count: 3
[DIFF_SYMBOLS] 🔑 Previous keys: [func1:12, func2:12]
[DIFF_SYMBOLS] 🔑 Current keys: [func1:12, func2:12, func3:12]
[DIFF_SYMBOLS] ✅ FOUND ADD: 'func3:12' -> func3 (Kind: 12)
[DIFF_SYMBOLS] 🎯 Generated 1 primitives:
  1. ADD function: 'func3'
```

The detailed logs will help us identify exactly where the diffing logic needs refinement! 🔍✨
