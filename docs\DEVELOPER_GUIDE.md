# Learning Docs Developer Guide

This guide provides detailed information for developers who want to understand, modify, or extend the Learning Docs extension.

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Project Structure](#project-structure)
- [Core Concepts](#core-concepts)
- [Service Architecture](#service-architecture)
- [UI Components](#ui-components)
- [Testing](#testing)
- [Debugging](#debugging)
- [Common Development Tasks](#common-development-tasks)

## Development Environment Setup

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)
- VS Code (v1.60 or later)
- Git

### Setup Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/ALPHAbilal/one_one.git
   cd one_one/learning-docs
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run compile
   ```

4. Run the extension:
   - Press F5 in VS Code to launch a new window with the extension loaded
   - Alternatively, run `code --new-window --extensionDevelopmentPath="$(pwd)"`

## Project Structure

The project is organized as follows:

```
learning-docs/
├── .vscode/            # VS Code settings
├── docs/               # Documentation
├── media/              # Media files
├── out/                # Compiled JavaScript files
├── src/                # Source code
│   ├── interfaces/     # TypeScript interfaces
│   ├── services/       # Core services
│   ├── ui/             # User interface components
│   └── extension.ts    # Extension entry point
├── CHANGELOG.md        # Changelog
├── CONTRIBUTING.md     # Contribution guidelines
├── LICENSE             # License file
├── package.json        # Extension manifest
├── README.md           # Main documentation
└── tsconfig.json       # TypeScript configuration
```

### Key Files

- `src/extension.ts`: The entry point of the extension
- `src/interfaces/*.ts`: TypeScript interfaces for services and data structures
- `src/services/*.ts`: Implementation of core services
- `src/ui/*.ts`: Implementation of UI components
- `package.json`: Extension manifest, including commands, configuration, and dependencies

## Core Concepts

### Extension Lifecycle

The extension follows the standard VS Code extension lifecycle:

1. **Activation**: The extension is activated when VS Code starts or when a command is executed
2. **Operation**: The extension runs and responds to events
3. **Deactivation**: The extension is deactivated when VS Code is closed

### Change Detection

The extension detects changes to files in the workspace using VS Code's file system watcher. Changes are processed and stored as documentation records.

### Documentation Records

Documentation records are the core data structure of the extension. Each record represents a change to a file and includes:

- Basic information (file path, timestamp, event type)
- Previous and current content
- Raw diff
- Semantic primitives
- Contextual information
- AI analysis

## Service Architecture

The extension is built with a modular architecture that consists of several services:

### Core Services

- **LoggerService**: Handles logging throughout the extension
- **ConfigService**: Manages configuration settings
- **WorkspaceService**: Provides workspace-related utilities
- **StorageService**: Handles storage and retrieval of documentation records

### Change Detection

- **ChangeEventAggregatorService**: Listens for file changes and emits stable change events
- **ChangeProcessorService**: Processes change events and orchestrates the analysis pipeline

### Analysis Services

- **FileHistoryService**: Retrieves previous versions of files
- **DiffService**: Generates diffs between versions
- **SemanticAnalyzerService**: Analyzes code at a structural level
- **ContextAnalyzerService**: Gathers contextual information about changes
- **AIDocumentationService**: Generates AI-powered documentation

### Service Dependencies

Services are initialized in the `extension.ts` file and injected into other services as needed. The dependency graph is as follows:

```
LoggerService <- (used by all services)
ConfigService <- WorkspaceService, SemanticAnalyzerService, ContextAnalyzerService, AIDocumentationService
WorkspaceService <- StorageService, FileHistoryService, ChangeEventAggregatorService, SemanticAnalyzerService, ContextAnalyzerService
StorageService <- ChangeProcessorService
FileHistoryService <- ChangeProcessorService
DiffService <- ChangeProcessorService
SemanticAnalyzerService <- ChangeProcessorService
ContextAnalyzerService <- ChangeProcessorService
AIDocumentationService <- ChangeProcessorService
ChangeEventAggregatorService <- ChangeProcessorService
```

## UI Components

The extension provides several UI components for interacting with documentation:

### DocumentationPanel

A WebView panel that displays documentation for a specific file or record. It generates HTML for different sections of the documentation.

### DocumentationTreeProvider

A TreeView provider that shows a tree of files and changes. It allows users to navigate through the documentation.

### StatusBarItem

A status bar item that provides quick access to the documentation panel.

## Testing

The extension includes a testing framework for unit and integration tests.

### Running Tests

To run tests:

```bash
npm test
```

### Writing Tests

Tests are located in the `src/test` directory. Each test file should have a `.test.ts` extension.

Example test:

```typescript
import * as assert from 'assert';
import { LoggerService } from '../services/logger-service';

suite('LoggerService Tests', () => {
    test('should log messages', () => {
        const loggerService = new LoggerService();
        // Test implementation
        assert.ok(true);
    });
});
```

## Debugging

### Debugging the Extension

To debug the extension:

1. Open the project in VS Code
2. Set breakpoints in the code
3. Press F5 to launch a new window with the extension in debug mode
4. Perform actions that trigger the breakpoints
5. Use the Debug Console to inspect variables and evaluate expressions

### Debugging the WebView

To debug the WebView:

1. Open the Developer Tools in the VS Code window where the extension is running
2. Click on the "Select an element in the page to inspect it" button
3. Click on the WebView
4. Use the Developer Tools to inspect the WebView content and debug JavaScript

## Common Development Tasks

### Adding a New Service

To add a new service:

1. Create a new interface in `src/interfaces/`
2. Create a new implementation in `src/services/`
3. Update `extension.ts` to initialize and use the new service

Example interface:

```typescript
export interface IMyService {
    doSomething(): void;
}
```

Example implementation:

```typescript
import { IMyService } from '../interfaces/my-service';
import { ILoggerService } from '../interfaces/logger';

export class MyService implements IMyService {
    constructor(
        private readonly loggerService: ILoggerService
    ) {}
    
    public doSomething(): void {
        this.loggerService.info('Doing something');
    }
}
```

### Adding a New Command

To add a new command:

1. Add the command to `package.json` in the `contributes.commands` section
2. Register the command in `extension.ts`

Example `package.json` entry:

```json
{
    "command": "learningDocs.myCommand",
    "title": "Learning Docs: My Command"
}
```

Example command registration:

```typescript
context.subscriptions.push(
    vscode.commands.registerCommand('learningDocs.myCommand', () => {
        // Command implementation
    })
);
```

### Adding a New Configuration Setting

To add a new configuration setting:

1. Add the setting to `package.json` in the `contributes.configuration.properties` section
2. Use the setting in the code through the `ConfigService`

Example `package.json` entry:

```json
{
    "learningDocs.mySetting": {
        "type": "boolean",
        "default": true,
        "description": "Enable/disable my setting"
    }
}
```

Example usage:

```typescript
const isEnabled = this.configService.getSetting<boolean>('mySetting');
```

### Modifying the WebView

To modify the WebView:

1. Update the HTML generation methods in `src/ui/documentation-panel.ts`
2. Test the changes by opening the Documentation Panel

Example HTML generation:

```typescript
private _getMyHtml(): string {
    return `
    <div class="section">
        <h2>My Section</h2>
        <p>My content</p>
    </div>`;
}
```
