import * as vscode from 'vscode';
import * as path from 'path';
import { ILoggerService } from '../interfaces/logger';
import { IStorageService } from '../interfaces/storage-service';
import { ChangeDocumentationRecord } from '../interfaces/storage';
import { SemanticPrimitive } from '../interfaces/semantic';

/**
 * Tree item for a semantic primitive.
 */
export class SemanticPrimitiveTreeItem extends vscode.TreeItem {
    constructor(
        public readonly primitive: SemanticPrimitive,
        public readonly recordId: string
    ) {
        super(SemanticPrimitiveTreeItem.getLabel(primitive), vscode.TreeItemCollapsibleState.None);

        this.tooltip = SemanticPrimitiveTreeItem.getTooltip(primitive);
        this.description = primitive.signature || '';
        this.id = `${recordId}-${primitive.elementName}-${primitive.operation}`;
        this.contextValue = 'semanticPrimitive';

        // Set the icon based on the operation type
        switch (primitive.operation) {
            case 'add':
                this.iconPath = new vscode.ThemeIcon('add', new vscode.ThemeColor('charts.green'));
                break;
            case 'remove':
                this.iconPath = new vscode.ThemeIcon('remove', new vscode.ThemeColor('charts.red'));
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit', new vscode.ThemeColor('charts.orange'));
                break;
            case 'rename':
                this.iconPath = new vscode.ThemeIcon('symbol-rename', new vscode.ThemeColor('charts.blue'));
                break;
        }

        // Set the command to show documentation (same as parent record)
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [recordId]
        };
    }

    /**
     * Get the label for a semantic primitive.
     * @param primitive The semantic primitive.
     * @returns The label for the primitive.
     */
    private static getLabel(primitive: SemanticPrimitive): string {
        const operationSymbol = {
            'add': '➕',
            'remove': '➖',
            'modify': '✏️',
            'rename': '🔄'
        }[primitive.operation];

        const elementTypeCapitalized = primitive.elementType.charAt(0).toUpperCase() + primitive.elementType.slice(1);

        if (primitive.operation === 'rename' && primitive.oldElementName) {
            return `${operationSymbol} ${elementTypeCapitalized}: '${primitive.oldElementName}' → '${primitive.elementName}'`;
        }

        return `${operationSymbol} ${elementTypeCapitalized}: '${primitive.elementName}'`;
    }

    /**
     * Get the tooltip for a semantic primitive.
     * @param primitive The semantic primitive.
     * @returns The tooltip for the primitive.
     */
    private static getTooltip(primitive: SemanticPrimitive): string {
        let tooltip = `${primitive.operation.toUpperCase()} ${primitive.elementType}: ${primitive.elementName}`;

        if (primitive.signature) {
            tooltip += `\nSignature: ${primitive.signature}`;
        }

        if (primitive.parentElement) {
            tooltip += `\nParent: ${primitive.parentElement.type} ${primitive.parentElement.name}`;
        }

        if (primitive.range) {
            tooltip += `\nLocation: Lines ${primitive.range.startLine + 1}-${primitive.range.endLine + 1}`;
        }

        return tooltip;
    }
}

/**
 * Tree item for a documentation record.
 */
export class DocumentationTreeItem extends vscode.TreeItem {
    constructor(
        public readonly record: ChangeDocumentationRecord,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(DocumentationTreeItem.getLabel(record), collapsibleState);

        this.tooltip = DocumentationTreeItem.getTooltip(record);
        this.description = record.timestamp;
        this.id = record.id;
        this.contextValue = 'documentationRecord';

        // Set the icon based on the event type
        switch (record.eventType) {
            case 'create':
                this.iconPath = new vscode.ThemeIcon('add');
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit');
                break;
            case 'delete':
                this.iconPath = new vscode.ThemeIcon('trash');
                break;
            case 'baseline':
                this.iconPath = new vscode.ThemeIcon('file');
                break;
        }

        // Set the command to open the documentation panel
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [record.id]
        };
    }

    /**
     * Get the label for a documentation record.
     * @param record The documentation record.
     * @returns The label for the record.
     */
    private static getLabel(record: ChangeDocumentationRecord): string {
        const fileName = path.basename(record.filePath);
        let baseLabel: string;

        switch (record.eventType) {
            case 'create':
                baseLabel = `Created ${fileName}`;
                break;
            case 'modify':
                baseLabel = `Modified ${fileName}`;
                break;
            case 'delete':
                baseLabel = `Deleted ${fileName}`;
                break;
            case 'baseline':
                baseLabel = `Baseline ${fileName}`;
                break;
            default:
                baseLabel = fileName;
        }

        // Add semantic primitives count if available
        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            const count = record.semanticPrimitives.length;
            baseLabel += ` (${count} semantic change${count > 1 ? 's' : ''})`;
        }

        return baseLabel;
    }

    /**
     * Get the tooltip for a documentation record.
     * @param record The documentation record.
     * @returns The tooltip for the record.
     */
    private static getTooltip(record: ChangeDocumentationRecord): string {
        let tooltip = `${record.filePath} (${record.timestamp})`;

        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            tooltip += '\n\nSemantic Changes:';
            record.semanticPrimitives.forEach(primitive => {
                const operationSymbol = {
                    'add': '➕',
                    'remove': '➖',
                    'modify': '✏️',
                    'rename': '🔄'
                }[primitive.operation];

                tooltip += `\n${operationSymbol} ${primitive.elementType}: ${primitive.elementName}`;
            });
        }

        return tooltip;
    }
}

/**
 * Tree item for a file.
 */
export class FileTreeItem extends vscode.TreeItem {
    constructor(
        public readonly filePath: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(path.basename(filePath), collapsibleState);

        this.tooltip = filePath;
        this.description = filePath;
        this.contextValue = 'file';
        this.iconPath = vscode.ThemeIcon.File;
    }
}

/**
 * Tree data provider for documentation records.
 */
export class DocumentationTreeProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | undefined | null | void> = new vscode.EventEmitter<vscode.TreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(
        private readonly loggerService: ILoggerService,
        private readonly storageService: IStorageService
    ) {}

    /**
     * Refresh the tree view.
     */
    public refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    /**
     * Get the tree item for a given element.
     * @param element The element to get the tree item for.
     * @returns The tree item for the element.
     */
    getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
        return element;
    }

    /**
     * Get the children of a given element.
     * @param element The element to get the children for.
     * @returns A promise that resolves to the children of the element.
     */
    async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {
        try {
            if (!element) {
                // Root level: show files with documentation
                return await this.getFileItems();
            } else if (element instanceof FileTreeItem) {
                // File level: show documentation records for the file
                return await this.getRecordItems(element.filePath);
            } else if (element instanceof DocumentationTreeItem) {
                // Documentation record level: show semantic primitives if available
                return this.getSemanticPrimitiveItems(element.record);
            } else {
                // No children for semantic primitives
                return [];
            }
        } catch (error) {
            this.loggerService.error(`Error getting tree items: ${error}`);
            return [];
        }
    }

    /**
     * Get the file items for the root level.
     * @returns A promise that resolves to the file items.
     */
    private async getFileItems(): Promise<FileTreeItem[]> {
        try {
            // Get all unique file paths from the storage
            const filePaths = await this.storageService.getAllFilePaths();

            // Create tree items for each file
            return filePaths.map(filePath => new FileTreeItem(
                filePath,
                vscode.TreeItemCollapsibleState.Collapsed
            ));
        } catch (error) {
            this.loggerService.error(`Error getting file items: ${error}`);
            return [];
        }
    }

    /**
     * Get the record items for a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the record items.
     */
    private async getRecordItems(filePath: string): Promise<DocumentationTreeItem[]> {
        try {
            this.loggerService.info(`🌳 Getting record items for file: ${filePath}`);

            // Get records for the file
            const records = await this.storageService.getRecordsByFile(filePath);
            this.loggerService.info(`📄 Found ${records.length} records for ${filePath}`);

            // Create tree items for each record
            return records.map((record, index) => {
                // Set collapsible state based on whether record has semantic primitives
                const hasSemanticPrimitives = record.semanticPrimitives && record.semanticPrimitives.length > 0;
                const collapsibleState = hasSemanticPrimitives
                    ? vscode.TreeItemCollapsibleState.Collapsed
                    : vscode.TreeItemCollapsibleState.None;

                this.loggerService.info(`  Record ${index + 1}: ${record.eventType} ${record.filePath}`);
                this.loggerService.info(`    - Has semantic primitives: ${hasSemanticPrimitives}`);
                this.loggerService.info(`    - Semantic primitives count: ${record.semanticPrimitives?.length || 0}`);
                this.loggerService.info(`    - Collapsible state: ${collapsibleState === vscode.TreeItemCollapsibleState.Collapsed ? 'Collapsed' : 'None'}`);

                if (hasSemanticPrimitives) {
                    record.semanticPrimitives!.forEach((primitive, primIndex) => {
                        this.loggerService.info(`      Primitive ${primIndex + 1}: ${primitive.operation} ${primitive.elementType} '${primitive.elementName}'`);
                    });
                }

                return new DocumentationTreeItem(record, collapsibleState);
            });
        } catch (error) {
            this.loggerService.error(`💥 Error getting record items: ${error}`);
            return [];
        }
    }

    /**
     * Get the semantic primitive items for a documentation record.
     * @param record The documentation record.
     * @returns An array of semantic primitive tree items.
     */
    private getSemanticPrimitiveItems(record: ChangeDocumentationRecord): SemanticPrimitiveTreeItem[] {
        this.loggerService.info(`🔧 Getting semantic primitive items for record: ${record.id}`);
        this.loggerService.info(`📊 Record has ${record.semanticPrimitives?.length || 0} semantic primitives`);

        if (!record.semanticPrimitives || record.semanticPrimitives.length === 0) {
            this.loggerService.info('⚠️ No semantic primitives found, returning empty array');
            return [];
        }

        const primitiveItems = record.semanticPrimitives.map((primitive, index) => {
            this.loggerService.info(`  Creating tree item ${index + 1}: ${primitive.operation} ${primitive.elementType} '${primitive.elementName}'`);
            return new SemanticPrimitiveTreeItem(primitive, record.id);
        });

        this.loggerService.info(`✅ Created ${primitiveItems.length} semantic primitive tree items`);
        return primitiveItems;
    }
}
