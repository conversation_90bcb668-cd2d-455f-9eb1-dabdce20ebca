import { ChangeDocumentationRecord } from './storage';

/**
 * Represents AI-generated documentation for a code change.
 */
export interface AIAnalysis {
    /**
     * Summary of the change in natural language.
     */
    summary: string;
    
    /**
     * Detailed explanation of what the change does.
     */
    explanation: string;
    
    /**
     * Potential impact of the change on the codebase.
     */
    impact?: string;
    
    /**
     * Suggestions for improvements or potential issues.
     */
    suggestions?: string[];
    
    /**
     * Learning resources related to the concepts in the change.
     */
    learningResources?: {
        title: string;
        url?: string;
        description: string;
    }[];
    
    /**
     * Code examples that demonstrate the concepts in the change.
     */
    codeExamples?: {
        title: string;
        code: string;
        explanation: string;
    }[];
}

/**
 * Interface for AI documentation service.
 */
export interface IAIDocumentationService {
    /**
     * Generate AI documentation for a change.
     * 
     * @param record The change documentation record.
     * @returns A promise that resolves to the AI analysis, or null if generation is not possible.
     */
    generateDocumentation(
        record: ChangeDocumentationRecord
    ): Promise<AIAnalysis | null>;
}
