import * as vscode from 'vscode';
import { SemanticPrimitive, SemanticAnalysisResult } from './semantic';

// Re-export the types for backward compatibility
export { SemanticPrimitive, SemanticAnalysisResult };

/**
 * Interface for semantic analyzer service.
 */
export interface ISemanticAnalyzerService {
    /**
     * Get semantic analysis for a change.
     *
     * @param previousContent The previous content of the file (can be undefined for new files).
     * @param currentContent The current content of the file.
     * @param uri The URI of the file for context and LSP calls.
     * @param languageId The language ID of the file.
     * @returns A promise that resolves to the semantic analysis result, or null if analysis is not possible.
     */
    getSemanticAnalysis(
        previousContent: string | undefined,
        currentContent: string,
        uri: vscode.Uri,
        languageId: string
    ): Promise<SemanticAnalysisResult | null>;
}
