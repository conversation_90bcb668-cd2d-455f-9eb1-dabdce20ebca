/**
 * Represents a semantic primitive (a structural element in code).
 */
export interface SemanticPrimitive {
    /**
     * Type of the primitive.
     */
    type: 'function' | 'class' | 'method' | 'property' | 'interface' | 'enum' | 'variable' | 'import' | 'export' | 'other';
    
    /**
     * Name of the primitive.
     */
    name: string;
    
    /**
     * Kind of change that occurred to this primitive.
     */
    changeType: 'added' | 'removed' | 'modified' | 'renamed';
    
    /**
     * Original name if the primitive was renamed.
     */
    originalName?: string;
    
    /**
     * Signature or detail of the primitive (e.g., function parameters, property type).
     */
    detail?: string;
    
    /**
     * Original detail if the primitive was modified.
     */
    originalDetail?: string;
    
    /**
     * Range in the document where this primitive is located.
     */
    range?: {
        startLine: number;
        startCharacter: number;
        endLine: number;
        endCharacter: number;
    };
    
    /**
     * Container name (e.g., class name for a method).
     */
    containerName?: string;
    
    /**
     * Children primitives (e.g., methods of a class).
     */
    children?: SemanticPrimitive[];
}

/**
 * Result of semantic analysis.
 */
export interface SemanticAnalysisResult {
    /**
     * List of semantic primitives extracted from the change.
     */
    primitives: SemanticPrimitive[];
    
    /**
     * Language ID of the analyzed file.
     */
    languageId: string;
}

/**
 * Interface for semantic analyzer service.
 */
export interface ISemanticAnalyzerService {
    /**
     * Get semantic analysis for a change.
     * 
     * @param filePath The path to the file.
     * @param previousContent The previous content of the file.
     * @param currentContent The current content of the file.
     * @returns A promise that resolves to the semantic analysis result, or null if analysis is not possible.
     */
    getSemanticAnalysis(
        filePath: string,
        previousContent: string | undefined,
        currentContent: string | undefined
    ): Promise<SemanticAnalysisResult | null>;
}
