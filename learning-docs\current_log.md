Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T08:48:39.717Z] [INFO] Starting workspace initialization
[2025-05-26T08:48:39.717Z] [WARN] No workspace folder found
[2025-05-26T08:48:39.718Z] [WARN] No workspace found, skipping initialization
[2025-05-26T08:48:39.720Z] [INFO] Starting change processor
[2025-05-26T08:48:39.721Z] [INFO] Starting to listen for file changes
[2025-05-26T08:48:39.723Z] [INFO] Learning Docs extension activated
Learning Docs extension deactivated
[2025-05-26T08:48:50.025Z] [INFO] Stopping change processor
[2025-05-26T08:48:50.025Z] [INFO] Stopping file change listeners
[2025-05-26T08:48:50.026Z] [INFO] Stopping file change listeners
Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T08:48:54.215Z] [INFO] Starting workspace initialization
[2025-05-26T08:48:54.244Z] [INFO] Workspace marked as initialized
[2025-05-26T08:48:54.244Z] [INFO] Resuming workspace tracking (existing data found)
[2025-05-26T08:48:54.245Z] [INFO] Workspace initialization completed
[2025-05-26T08:48:54.245Z] [INFO] Starting change processor
[2025-05-26T08:48:54.246Z] [INFO] Starting to listen for file changes
[2025-05-26T08:48:54.247Z] [INFO] Learning Docs extension activated
[2025-05-26T08:49:53.393Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:49:53.393Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.395Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:49:53.396Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:49:53.410Z] [INFO] File created: requirements.txt
[2025-05-26T08:49:53.410Z] [INFO] Processing change: create - requirements.txt
[2025-05-26T08:49:53.412Z] [INFO] Generated diff for: requirements.txt
[2025-05-26T08:49:53.412Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for requirements.txt
[2025-05-26T08:49:53.412Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:49:53.413Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:49:53.413Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:49:53.413Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.414Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:49:53.414Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:49:53.415Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:49:53.415Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:49:53.415Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:49:53.416Z] [INFO]   - Event type: create
[2025-05-26T08:49:53.416Z] [INFO]   - Has current content: true
[2025-05-26T08:49:53.416Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:49:53.416Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:49:53.417Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.417Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:49:53.418Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:49:53.418Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:49:53.418Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for requirements.txt
[2025-05-26T08:49:53.419Z] [INFO] 📝 Detected language: plaintext
[2025-05-26T08:49:53.420Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/requirements.txt
[2025-05-26T08:49:53.420Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\requirements.txt
[2025-05-26T08:49:53.421Z] [INFO] 📝 Language: plaintext
[2025-05-26T08:49:53.421Z] [INFO] 📄 Current content length: 13
[2025-05-26T08:49:53.422Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:49:53.422Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:49:53.422Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:49:53.423Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.423Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:49:53.424Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:49:53.424Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:49:53.424Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:49:53.425Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:49:53.425Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.426Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:49:53.426Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:49:53.427Z] [INFO] 🌐 Language plaintext supported: false
[2025-05-26T08:49:53.427Z] [WARN] ❌ Semantic analysis not enabled for language: plaintext
[2025-05-26T08:49:53.429Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for requirements.txt
[2025-05-26T08:49:53.429Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:49:53.429Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:49:53.430Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:53.431Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:49:53.431Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:49:53.431Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:49:53.432Z] [INFO] Getting contextual info for: requirements.txt
[2025-05-26T08:49:53.592Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- requirements.txt
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:49:54.363Z] [INFO] Contextual analysis completed for: requirements.txt
[2025-05-26T08:49:54.363Z] [INFO] Contextual analysis completed for requirements.txt
[2025-05-26T08:49:54.364Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:49:54.364Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:49:54.364Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:54.365Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:49:54.365Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:49:54.365Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:49:54.366Z] [INFO] Generating AI documentation for: requirements.txt
[2025-05-26T08:49:54.366Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:49:54.366Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:49:54.367Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:54.367Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:49:54.367Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:49:54.368Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:49:54.368Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:49:54.368Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:49:54.369Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:49:54.369Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:49:54.379Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:49:54.379Z] [WARN] Failed to generate AI documentation for: requirements.txt
[2025-05-26T08:49:54.380Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 06b767e086eb8da8e16936f371a5ca40
[2025-05-26T08:49:54.380Z] [INFO] 📄 File: requirements.txt
[2025-05-26T08:49:54.380Z] [INFO] 🔄 Event type: create
[2025-05-26T08:49:54.381Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:49:54.384Z] [INFO] ✅ SAVED documentation record: 06b767e086eb8da8e16936f371a5ca40 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\06b767e086eb8da8e16936f371a5ca40.json
[2025-05-26T08:49:54.387Z] [INFO] Successfully processed change: 06b767e086eb8da8e16936f371a5ca40
[2025-05-26T08:50:06.211Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:06.211Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.212Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:06.213Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:06.643Z] [INFO] File created: app.py
[2025-05-26T08:50:06.644Z] [INFO] Processing change: create - app.py
[2025-05-26T08:50:06.645Z] [INFO] Generated diff for: app.py
[2025-05-26T08:50:06.645Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for app.py
[2025-05-26T08:50:06.646Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:50:06.646Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:06.646Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:06.647Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.648Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:06.648Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:06.649Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:06.649Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:50:06.649Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:50:06.650Z] [INFO]   - Event type: create
[2025-05-26T08:50:06.650Z] [INFO]   - Has current content: true
[2025-05-26T08:50:06.650Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:06.651Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:06.651Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.652Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:06.653Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:06.655Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:06.655Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for app.py
[2025-05-26T08:50:06.656Z] [INFO] 📝 Detected language: python
[2025-05-26T08:50:06.668Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/app.py
[2025-05-26T08:50:06.672Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\app.py
[2025-05-26T08:50:06.674Z] [INFO] 📝 Language: python
[2025-05-26T08:50:06.675Z] [INFO] 📄 Current content length: 2311
[2025-05-26T08:50:06.676Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:50:06.677Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:06.678Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:06.679Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.682Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:06.682Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:06.684Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:06.687Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:50:06.688Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:50:06.689Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.690Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:50:06.691Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:50:06.691Z] [INFO] 🌐 Language python supported: true
[2025-05-26T08:50:06.692Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\flask_learning\app.py
[2025-05-26T08:50:06.692Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T08:50:06.695Z] [INFO] 🔍 Getting document symbols for python (2311 chars)
[2025-05-26T08:50:06.697Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/app.py
[2025-05-26T08:50:06.698Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T08:50:06.896Z] [INFO] 📊 LSP returned 10 symbols from open document
[2025-05-26T08:50:06.896Z] [INFO] ✅ Found 10 current symbols
[2025-05-26T08:50:06.897Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T08:50:06.897Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T08:50:06.898Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T08:50:06.898Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 10
[2025-05-26T08:50:06.899Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS:
[2025-05-26T08:50:06.899Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS:
[2025-05-26T08:50:06.899Z] [INFO]   1. app (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.900Z] [INFO]   2. users (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.900Z] [INFO]   3. messages (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.900Z] [INFO]   4. home (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.901Z] [INFO]   5. about (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.902Z] [INFO]   6. user_profile (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.902Z] [INFO]     1. username (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.903Z] [INFO]   7. contact (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.903Z] [INFO]     1. name (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.904Z] [INFO]     2. email (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.905Z] [INFO]     3. message (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.905Z] [INFO]   8. view_messages (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.905Z] [INFO]   9. api_users (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.906Z] [INFO]   10. page_not_found (Kind: 11, Detail: 'none')
[2025-05-26T08:50:06.907Z] [INFO]     1. error (Kind: 12, Detail: 'none')
[2025-05-26T08:50:06.907Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T08:50:06.908Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.909Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T08:50:06.909Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T08:50:06.910Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T08:50:06.910Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T08:50:06.911Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 15 symbols
[2025-05-26T08:50:06.911Z] [INFO] [DIFF_SYMBOLS] 🔑 Previous keys: []
[2025-05-26T08:50:06.911Z] [INFO] [DIFF_SYMBOLS] 🔑 Current keys: [app:12, users:12, messages:12, home:11, about:11, user_profile:11, user_profile.username:12, contact:11, contact.name:12, contact.email:12, contact.message:12, view_messages:11, api_users:11, page_not_found:11, page_not_found.error:12]
[2025-05-26T08:50:06.912Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T08:50:06.912Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T08:50:06.913Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users:12' -> users (Kind: 12)
[2025-05-26T08:50:06.913Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'messages:12' -> messages (Kind: 12)
[2025-05-26T08:50:06.913Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T08:50:06.914Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T08:50:06.914Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_profile:11' -> user_profile (Kind: 11)
[2025-05-26T08:50:06.915Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_profile.username:12' -> username (Kind: 12)
[2025-05-26T08:50:06.915Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact:11' -> contact (Kind: 11)
[2025-05-26T08:50:06.916Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.name:12' -> name (Kind: 12)
[2025-05-26T08:50:06.916Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.email:12' -> email (Kind: 12)
[2025-05-26T08:50:06.917Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.message:12' -> message (Kind: 12)
[2025-05-26T08:50:06.917Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'view_messages:11' -> view_messages (Kind: 11)
[2025-05-26T08:50:06.918Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'api_users:11' -> api_users (Kind: 11)
[2025-05-26T08:50:06.918Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found:11' -> page_not_found (Kind: 11)
[2025-05-26T08:50:06.918Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found.error:12' -> error (Kind: 12)
[2025-05-26T08:50:06.919Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T08:50:06.920Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T08:50:06.921Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T08:50:06.922Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.922Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T08:50:06.923Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T08:50:06.925Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T08:50:06.926Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 15 primitives:
[2025-05-26T08:50:06.926Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T08:50:06.927Z] [INFO]   2. ADD variable: 'users'
[2025-05-26T08:50:06.927Z] [INFO]   3. ADD variable: 'messages'
[2025-05-26T08:50:06.928Z] [INFO]   4. ADD function: 'home'
[2025-05-26T08:50:06.928Z] [INFO]   5. ADD function: 'about'
[2025-05-26T08:50:06.929Z] [INFO]   6. ADD function: 'user_profile'
[2025-05-26T08:50:06.929Z] [INFO]   7. ADD variable: 'username'
[2025-05-26T08:50:06.930Z] [INFO]   8. ADD function: 'contact'
[2025-05-26T08:50:06.930Z] [INFO]   9. ADD variable: 'name'
[2025-05-26T08:50:06.931Z] [INFO]   10. ADD variable: 'email'
[2025-05-26T08:50:06.931Z] [INFO]   11. ADD variable: 'message'
[2025-05-26T08:50:06.932Z] [INFO]   12. ADD function: 'view_messages'
[2025-05-26T08:50:06.932Z] [INFO]   13. ADD function: 'api_users'
[2025-05-26T08:50:06.933Z] [INFO]   14. ADD function: 'page_not_found'
[2025-05-26T08:50:06.934Z] [INFO]   15. ADD variable: 'error'
[2025-05-26T08:50:06.934Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T08:50:06.935Z] [INFO] 🎯 Generated 15 semantic primitives
[2025-05-26T08:50:06.936Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T08:50:06.937Z] [INFO]   2. ADD variable: 'users'
[2025-05-26T08:50:06.938Z] [INFO]   3. ADD variable: 'messages'
[2025-05-26T08:50:06.938Z] [INFO]   4. ADD function: 'home'
[2025-05-26T08:50:06.939Z] [INFO]   5. ADD function: 'about'
[2025-05-26T08:50:06.939Z] [INFO]   6. ADD function: 'user_profile'
[2025-05-26T08:50:06.940Z] [INFO]   7. ADD variable: 'username'
[2025-05-26T08:50:06.940Z] [INFO]   8. ADD function: 'contact'
[2025-05-26T08:50:06.941Z] [INFO]   9. ADD variable: 'name'
[2025-05-26T08:50:06.941Z] [INFO]   10. ADD variable: 'email'
[2025-05-26T08:50:06.942Z] [INFO]   11. ADD variable: 'message'
[2025-05-26T08:50:06.942Z] [INFO]   12. ADD function: 'view_messages'
[2025-05-26T08:50:06.943Z] [INFO]   13. ADD function: 'api_users'
[2025-05-26T08:50:06.945Z] [INFO]   14. ADD function: 'page_not_found'
[2025-05-26T08:50:06.945Z] [INFO]   15. ADD variable: 'error'
[2025-05-26T08:50:06.946Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T08:50:06.946Z] [INFO] 📊 Summary: +15 -0 ~0 ↔0
[2025-05-26T08:50:06.947Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 15 primitives found
[2025-05-26T08:50:06.950Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for app.py: 15 primitives found
[2025-05-26T08:50:06.950Z] [INFO] 📊 Primitives stored in record: 15
[2025-05-26T08:50:06.950Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:50:06.951Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:50:06.951Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:06.953Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:06.954Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:50:06.954Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:50:06.955Z] [INFO] Getting contextual info for: app.py
[2025-05-26T08:50:07.850Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- app.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:50:08.054Z] [INFO] Contextual analysis completed for: app.py
[2025-05-26T08:50:08.055Z] [INFO] Contextual analysis completed for app.py
[2025-05-26T08:50:08.055Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:08.056Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:08.056Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:08.057Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:08.057Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:08.058Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:08.058Z] [INFO] Generating AI documentation for: app.py
[2025-05-26T08:50:08.058Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:08.059Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:08.059Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:08.059Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:08.060Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:08.060Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:08.061Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:50:08.061Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:08.061Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:50:08.062Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:50:08.065Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:50:08.066Z] [WARN] Failed to generate AI documentation for: app.py
[2025-05-26T08:50:08.066Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: d07d53a5e13e8f00414a4e302ed849d9
[2025-05-26T08:50:08.066Z] [INFO] 📄 File: app.py
[2025-05-26T08:50:08.067Z] [INFO] 🔄 Event type: create
[2025-05-26T08:50:08.067Z] [INFO] 🔧 Semantic primitives: 15
[2025-05-26T08:50:08.067Z] [INFO]   1. add variable: 'app'
[2025-05-26T08:50:08.068Z] [INFO]   2. add variable: 'users'
[2025-05-26T08:50:08.068Z] [INFO]   3. add variable: 'messages'
[2025-05-26T08:50:08.068Z] [INFO]   4. add function: 'home'
[2025-05-26T08:50:08.069Z] [INFO]   5. add function: 'about'
[2025-05-26T08:50:08.069Z] [INFO]   6. add function: 'user_profile'
[2025-05-26T08:50:08.070Z] [INFO]   7. add variable: 'username'
[2025-05-26T08:50:08.070Z] [INFO]   8. add function: 'contact'
[2025-05-26T08:50:08.071Z] [INFO]   9. add variable: 'name'
[2025-05-26T08:50:08.071Z] [INFO]   10. add variable: 'email'
[2025-05-26T08:50:08.071Z] [INFO]   11. add variable: 'message'
[2025-05-26T08:50:08.072Z] [INFO]   12. add function: 'view_messages'
[2025-05-26T08:50:08.072Z] [INFO]   13. add function: 'api_users'
[2025-05-26T08:50:08.072Z] [INFO]   14. add function: 'page_not_found'
[2025-05-26T08:50:08.073Z] [INFO]   15. add variable: 'error'
[2025-05-26T08:50:08.095Z] [INFO] ✅ SAVED documentation record: d07d53a5e13e8f00414a4e302ed849d9 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\d07d53a5e13e8f00414a4e302ed849d9.json
[2025-05-26T08:50:08.104Z] [INFO] Successfully processed change: d07d53a5e13e8f00414a4e302ed849d9
[2025-05-26T08:50:16.071Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:16.072Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.073Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:16.073Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:16.215Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:16.216Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.217Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:16.217Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:16.220Z] [INFO] Directory created: templates (skipping content analysis)
[2025-05-26T08:50:16.238Z] [INFO] File created: templates/base.html
[2025-05-26T08:50:16.239Z] [INFO] Processing change: create - templates/base.html
[2025-05-26T08:50:16.239Z] [INFO] Generated diff for: templates/base.html
[2025-05-26T08:50:16.240Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/base.html
[2025-05-26T08:50:16.240Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:50:16.240Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:16.241Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:16.241Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.242Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:16.242Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:16.242Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:16.242Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:50:16.243Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:50:16.243Z] [INFO]   - Event type: create
[2025-05-26T08:50:16.243Z] [INFO]   - Has current content: true
[2025-05-26T08:50:16.243Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:16.243Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:16.244Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.244Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:16.244Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:16.245Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:16.245Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/base.html
[2025-05-26T08:50:16.245Z] [INFO] 📝 Detected language: html
[2025-05-26T08:50:16.245Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/base.html
[2025-05-26T08:50:16.246Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\base.html
[2025-05-26T08:50:16.246Z] [INFO] 📝 Language: html
[2025-05-26T08:50:16.246Z] [INFO] 📄 Current content length: 1484
[2025-05-26T08:50:16.247Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:50:16.247Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:16.247Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:16.248Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.248Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:16.249Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:16.249Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:16.250Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:50:16.250Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:50:16.250Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.251Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:50:16.251Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:50:16.252Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:50:16.253Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:50:16.254Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/base.html
[2025-05-26T08:50:16.255Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:50:16.255Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:50:16.256Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.256Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:16.256Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:50:16.257Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:50:16.257Z] [INFO] Getting contextual info for: templates/base.html
[2025-05-26T08:50:16.350Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/base.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:50:16.551Z] [INFO] Contextual analysis completed for: templates/base.html
[2025-05-26T08:50:16.552Z] [INFO] Contextual analysis completed for templates/base.html
[2025-05-26T08:50:16.552Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:16.553Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:16.553Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.554Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:16.555Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:16.555Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:16.555Z] [INFO] Generating AI documentation for: templates/base.html
[2025-05-26T08:50:16.555Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:16.556Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:16.556Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.557Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:16.557Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:16.557Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:16.557Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:50:16.558Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:16.558Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:50:16.558Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:50:16.561Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:50:16.562Z] [WARN] Failed to generate AI documentation for: templates/base.html
[2025-05-26T08:50:16.562Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 205ab072c257335b03891edeeb67d35f
[2025-05-26T08:50:16.563Z] [INFO] 📄 File: templates/base.html
[2025-05-26T08:50:16.563Z] [INFO] 🔄 Event type: create
[2025-05-26T08:50:16.563Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:50:16.572Z] [INFO] ✅ SAVED documentation record: 205ab072c257335b03891edeeb67d35f to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\205ab072c257335b03891edeeb67d35f.json
[2025-05-26T08:50:16.590Z] [INFO] Successfully processed change: 205ab072c257335b03891edeeb67d35f
[2025-05-26T08:50:27.951Z] [INFO] 🌳 Getting record items for file: README.md
[2025-05-26T08:50:27.953Z] [INFO] 📂 LOADING RECORDS for file: README.md
[2025-05-26T08:50:27.955Z] [INFO] 📁 Found 15 files in workspace directory
[2025-05-26T08:50:27.961Z] [INFO] 📄 Found matching record: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:50:27.962Z] [INFO]   - Event type: create
[2025-05-26T08:50:27.962Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:50:27.964Z] [INFO] ✅ LOADED 1 records for README.md
[2025-05-26T08:50:27.965Z] [INFO] 📄 Found 1 records for README.md
[2025-05-26T08:50:27.965Z] [INFO]   Record 1: create README.md
[2025-05-26T08:50:27.965Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:50:27.966Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:50:27.966Z] [INFO]     - Collapsible state: None
[2025-05-26T08:50:28.489Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:28.489Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.490Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:28.491Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:28.494Z] [INFO] File created: templates/index.html
[2025-05-26T08:50:28.495Z] [INFO] Processing change: create - templates/index.html
[2025-05-26T08:50:28.495Z] [INFO] Generated diff for: templates/index.html
[2025-05-26T08:50:28.495Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/index.html
[2025-05-26T08:50:28.496Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:50:28.496Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:28.496Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:28.496Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.497Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:28.497Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:28.498Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:28.498Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:50:28.498Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:50:28.499Z] [INFO]   - Event type: create
[2025-05-26T08:50:28.499Z] [INFO]   - Has current content: true
[2025-05-26T08:50:28.499Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:28.499Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:28.499Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.500Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:28.500Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:28.500Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:28.501Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/index.html
[2025-05-26T08:50:28.501Z] [INFO] 📝 Detected language: html
[2025-05-26T08:50:28.501Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/index.html
[2025-05-26T08:50:28.501Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\index.html
[2025-05-26T08:50:28.502Z] [INFO] 📝 Language: html
[2025-05-26T08:50:28.502Z] [INFO] 📄 Current content length: 2093
[2025-05-26T08:50:28.502Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:50:28.503Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:28.503Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:28.504Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.505Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:28.506Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:28.506Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:28.507Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:50:28.508Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:50:28.508Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.509Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:50:28.509Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:50:28.510Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:50:28.511Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:50:28.512Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/index.html
[2025-05-26T08:50:28.513Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:50:28.513Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:50:28.514Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.515Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:28.515Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:50:28.515Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:50:28.516Z] [INFO] Getting contextual info for: templates/index.html
[2025-05-26T08:50:28.615Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/index.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:50:28.747Z] [INFO] Showing documentation for record: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:50:28.871Z] [INFO] Contextual analysis completed for: templates/index.html
[2025-05-26T08:50:28.872Z] [INFO] Contextual analysis completed for templates/index.html
[2025-05-26T08:50:28.872Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:28.872Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:28.872Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.873Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:28.874Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:28.874Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:28.874Z] [INFO] Generating AI documentation for: templates/index.html
[2025-05-26T08:50:28.875Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:28.875Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:28.876Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.876Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:28.877Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:28.877Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:28.878Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:50:28.879Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:28.879Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:50:28.880Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:50:28.884Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:50:28.885Z] [WARN] Failed to generate AI documentation for: templates/index.html
[2025-05-26T08:50:28.886Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 0ff42efd3ffd11fc3ae0e9d1812962ca
[2025-05-26T08:50:28.887Z] [INFO] 📄 File: templates/index.html
[2025-05-26T08:50:28.887Z] [INFO] 🔄 Event type: create
[2025-05-26T08:50:28.888Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:50:28.898Z] [INFO] ✅ SAVED documentation record: 0ff42efd3ffd11fc3ae0e9d1812962ca to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\0ff42efd3ffd11fc3ae0e9d1812962ca.json
[2025-05-26T08:50:28.907Z] [INFO] Successfully processed change: 0ff42efd3ffd11fc3ae0e9d1812962ca
[2025-05-26T08:50:44.669Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:44.670Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.670Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:44.671Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:44.674Z] [INFO] File created: templates/about.html
[2025-05-26T08:50:44.675Z] [INFO] Processing change: create - templates/about.html
[2025-05-26T08:50:44.675Z] [INFO] Generated diff for: templates/about.html
[2025-05-26T08:50:44.676Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/about.html
[2025-05-26T08:50:44.676Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:50:44.676Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:44.677Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:44.677Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.677Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:44.678Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:44.678Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:44.678Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:50:44.678Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:50:44.679Z] [INFO]   - Event type: create
[2025-05-26T08:50:44.679Z] [INFO]   - Has current content: true
[2025-05-26T08:50:44.679Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:44.679Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:44.679Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.680Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:44.680Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:44.681Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:44.681Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/about.html
[2025-05-26T08:50:44.681Z] [INFO] 📝 Detected language: html
[2025-05-26T08:50:44.681Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/about.html
[2025-05-26T08:50:44.682Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\about.html
[2025-05-26T08:50:44.682Z] [INFO] 📝 Language: html
[2025-05-26T08:50:44.682Z] [INFO] 📄 Current content length: 2415
[2025-05-26T08:50:44.682Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:50:44.683Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:44.683Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:44.683Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.684Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:44.684Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:44.684Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:44.684Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:50:44.685Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:50:44.685Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.686Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:50:44.686Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:50:44.687Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:50:44.687Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:50:44.688Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/about.html
[2025-05-26T08:50:44.688Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:50:44.689Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:50:44.689Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:44.689Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:44.690Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:50:44.690Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:50:44.690Z] [INFO] Getting contextual info for: templates/about.html
[2025-05-26T08:50:44.779Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/about.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:50:45.010Z] [INFO] Contextual analysis completed for: templates/about.html
[2025-05-26T08:50:45.010Z] [INFO] Contextual analysis completed for templates/about.html
[2025-05-26T08:50:45.011Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:45.011Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:45.011Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:45.012Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:45.012Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:45.012Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:45.012Z] [INFO] Generating AI documentation for: templates/about.html
[2025-05-26T08:50:45.013Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:45.013Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:45.013Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:45.014Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:45.014Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:45.014Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:45.015Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:50:45.015Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:45.015Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:50:45.016Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:50:45.020Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:50:45.020Z] [WARN] Failed to generate AI documentation for: templates/about.html
[2025-05-26T08:50:45.021Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: cc99465966a2550cd8d395ce6201b371
[2025-05-26T08:50:45.021Z] [INFO] 📄 File: templates/about.html
[2025-05-26T08:50:45.021Z] [INFO] 🔄 Event type: create
[2025-05-26T08:50:45.022Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:50:45.030Z] [INFO] ✅ SAVED documentation record: cc99465966a2550cd8d395ce6201b371 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\cc99465966a2550cd8d395ce6201b371.json
[2025-05-26T08:50:45.037Z] [INFO] Successfully processed change: cc99465966a2550cd8d395ce6201b371
[2025-05-26T08:50:56.845Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:50:56.846Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.847Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:50:56.847Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:50:56.850Z] [INFO] File created: templates/contact.html
[2025-05-26T08:50:56.851Z] [INFO] Processing change: create - templates/contact.html
[2025-05-26T08:50:56.852Z] [INFO] Generated diff for: templates/contact.html
[2025-05-26T08:50:56.852Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/contact.html
[2025-05-26T08:50:56.852Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:50:56.853Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:56.853Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:56.853Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.854Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:56.854Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:56.855Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:56.855Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:50:56.855Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:50:56.855Z] [INFO]   - Event type: create
[2025-05-26T08:50:56.855Z] [INFO]   - Has current content: true
[2025-05-26T08:50:56.856Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:56.856Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:56.856Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.857Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:56.857Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:56.857Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:56.857Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/contact.html
[2025-05-26T08:50:56.858Z] [INFO] 📝 Detected language: html
[2025-05-26T08:50:56.858Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/contact.html
[2025-05-26T08:50:56.859Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\contact.html
[2025-05-26T08:50:56.859Z] [INFO] 📝 Language: html
[2025-05-26T08:50:56.859Z] [INFO] 📄 Current content length: 2221
[2025-05-26T08:50:56.859Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:50:56.860Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:50:56.860Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:50:56.860Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.861Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:56.862Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:50:56.862Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:50:56.862Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:50:56.862Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:50:56.863Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.863Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:50:56.863Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:50:56.864Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:50:56.864Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:50:56.865Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/contact.html
[2025-05-26T08:50:56.865Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:50:56.865Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:50:56.866Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:56.866Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:50:56.866Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:50:56.867Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:50:56.867Z] [INFO] Getting contextual info for: templates/contact.html
[2025-05-26T08:50:56.957Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/contact.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:50:57.219Z] [INFO] Contextual analysis completed for: templates/contact.html
[2025-05-26T08:50:57.220Z] [INFO] Contextual analysis completed for templates/contact.html
[2025-05-26T08:50:57.221Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:57.221Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:57.221Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:57.222Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:57.222Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:57.223Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:57.223Z] [INFO] Generating AI documentation for: templates/contact.html
[2025-05-26T08:50:57.223Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:50:57.223Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:50:57.224Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:57.224Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:50:57.224Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:50:57.225Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:50:57.225Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:50:57.225Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:50:57.226Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:50:57.226Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:50:57.229Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:50:57.230Z] [WARN] Failed to generate AI documentation for: templates/contact.html
[2025-05-26T08:50:57.230Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 08a868d834764ddb4d66c3ee68bf93d1
[2025-05-26T08:50:57.230Z] [INFO] 📄 File: templates/contact.html
[2025-05-26T08:50:57.231Z] [INFO] 🔄 Event type: create
[2025-05-26T08:50:57.231Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:50:57.239Z] [INFO] ✅ SAVED documentation record: 08a868d834764ddb4d66c3ee68bf93d1 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\08a868d834764ddb4d66c3ee68bf93d1.json
[2025-05-26T08:50:57.252Z] [INFO] Successfully processed change: 08a868d834764ddb4d66c3ee68bf93d1
[2025-05-26T08:51:10.231Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:51:10.231Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.232Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:51:10.233Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:51:10.237Z] [INFO] File created: templates/user_profile.html
[2025-05-26T08:51:10.238Z] [INFO] Processing change: create - templates/user_profile.html
[2025-05-26T08:51:10.238Z] [INFO] Generated diff for: templates/user_profile.html
[2025-05-26T08:51:10.239Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/user_profile.html
[2025-05-26T08:51:10.239Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:51:10.239Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:10.239Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:10.240Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.240Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:10.240Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:10.241Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:10.241Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:51:10.241Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:51:10.241Z] [INFO]   - Event type: create
[2025-05-26T08:51:10.242Z] [INFO]   - Has current content: true
[2025-05-26T08:51:10.242Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:10.242Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:10.242Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.243Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:10.243Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:10.244Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:10.244Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/user_profile.html
[2025-05-26T08:51:10.245Z] [INFO] 📝 Detected language: html
[2025-05-26T08:51:10.245Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/user_profile.html
[2025-05-26T08:51:10.245Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\user_profile.html
[2025-05-26T08:51:10.246Z] [INFO] 📝 Language: html
[2025-05-26T08:51:10.246Z] [INFO] 📄 Current content length: 2190
[2025-05-26T08:51:10.246Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:51:10.246Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:10.247Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:10.247Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.248Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:10.248Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:10.248Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:10.249Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:51:10.249Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:51:10.249Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.250Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:51:10.250Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:51:10.251Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:51:10.251Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:51:10.252Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/user_profile.html
[2025-05-26T08:51:10.252Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:51:10.253Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:51:10.253Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.254Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:10.255Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:51:10.255Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:51:10.255Z] [INFO] Getting contextual info for: templates/user_profile.html
[2025-05-26T08:51:10.355Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/user_profile.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:51:10.571Z] [INFO] Contextual analysis completed for: templates/user_profile.html
[2025-05-26T08:51:10.571Z] [INFO] Contextual analysis completed for templates/user_profile.html
[2025-05-26T08:51:10.572Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:10.572Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:10.572Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.573Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:10.573Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:10.573Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:10.574Z] [INFO] Generating AI documentation for: templates/user_profile.html
[2025-05-26T08:51:10.574Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:10.574Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:10.575Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.575Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:10.576Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:10.576Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:10.576Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:51:10.576Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:10.577Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:51:10.577Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:51:10.580Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:51:10.581Z] [WARN] Failed to generate AI documentation for: templates/user_profile.html
[2025-05-26T08:51:10.581Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 54001b624fefe417f7ccfb1c85228b89
[2025-05-26T08:51:10.582Z] [INFO] 📄 File: templates/user_profile.html
[2025-05-26T08:51:10.582Z] [INFO] 🔄 Event type: create
[2025-05-26T08:51:10.582Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:51:10.591Z] [INFO] ✅ SAVED documentation record: 54001b624fefe417f7ccfb1c85228b89 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\54001b624fefe417f7ccfb1c85228b89.json
[2025-05-26T08:51:10.606Z] [INFO] Successfully processed change: 54001b624fefe417f7ccfb1c85228b89
[2025-05-26T08:51:21.244Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:51:21.245Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.246Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:51:21.246Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:51:21.250Z] [INFO] File created: templates/messages.html
[2025-05-26T08:51:21.250Z] [INFO] Processing change: create - templates/messages.html
[2025-05-26T08:51:21.251Z] [INFO] Generated diff for: templates/messages.html
[2025-05-26T08:51:21.251Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/messages.html
[2025-05-26T08:51:21.251Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:51:21.252Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:21.252Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:21.252Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.253Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:21.254Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:21.254Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:21.255Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:51:21.255Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:51:21.255Z] [INFO]   - Event type: create
[2025-05-26T08:51:21.255Z] [INFO]   - Has current content: true
[2025-05-26T08:51:21.256Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:21.256Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:21.256Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.256Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:21.257Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:21.257Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:21.257Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/messages.html
[2025-05-26T08:51:21.257Z] [INFO] 📝 Detected language: html
[2025-05-26T08:51:21.258Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/messages.html
[2025-05-26T08:51:21.258Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\messages.html
[2025-05-26T08:51:21.258Z] [INFO] 📝 Language: html
[2025-05-26T08:51:21.259Z] [INFO] 📄 Current content length: 1776
[2025-05-26T08:51:21.259Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:51:21.259Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:21.260Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:21.260Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.260Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:21.261Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:21.261Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:21.261Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:51:21.262Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:51:21.262Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.262Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:51:21.263Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:51:21.263Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:51:21.263Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:51:21.264Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/messages.html
[2025-05-26T08:51:21.264Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:51:21.265Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:51:21.265Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.265Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:21.266Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:51:21.266Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:51:21.266Z] [INFO] Getting contextual info for: templates/messages.html
[2025-05-26T08:51:21.372Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/messages.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:51:21.634Z] [INFO] Contextual analysis completed for: templates/messages.html
[2025-05-26T08:51:21.635Z] [INFO] Contextual analysis completed for templates/messages.html
[2025-05-26T08:51:21.635Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:21.636Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:21.636Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.637Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:21.638Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:21.638Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:21.638Z] [INFO] Generating AI documentation for: templates/messages.html
[2025-05-26T08:51:21.639Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:21.639Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:21.639Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.640Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:21.640Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:21.641Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:21.641Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:51:21.641Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:21.642Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:51:21.642Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:51:21.651Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:51:21.651Z] [WARN] Failed to generate AI documentation for: templates/messages.html
[2025-05-26T08:51:21.653Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 6c9641bd28e23ea4c85ce22702b35eab
[2025-05-26T08:51:21.654Z] [INFO] 📄 File: templates/messages.html
[2025-05-26T08:51:21.654Z] [INFO] 🔄 Event type: create
[2025-05-26T08:51:21.655Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:51:21.683Z] [INFO] ✅ SAVED documentation record: 6c9641bd28e23ea4c85ce22702b35eab to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\6c9641bd28e23ea4c85ce22702b35eab.json
[2025-05-26T08:51:21.691Z] [INFO] Successfully processed change: 6c9641bd28e23ea4c85ce22702b35eab
[2025-05-26T08:51:31.975Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:51:31.976Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.977Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:51:31.977Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:51:31.981Z] [INFO] File created: templates/404.html
[2025-05-26T08:51:31.981Z] [INFO] Processing change: create - templates/404.html
[2025-05-26T08:51:31.982Z] [INFO] Generated diff for: templates/404.html
[2025-05-26T08:51:31.982Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/404.html
[2025-05-26T08:51:31.982Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:51:31.982Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:31.983Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:31.983Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.983Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:31.984Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:31.984Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:31.984Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:51:31.984Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:51:31.984Z] [INFO]   - Event type: create
[2025-05-26T08:51:31.985Z] [INFO]   - Has current content: true
[2025-05-26T08:51:31.985Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:31.985Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:31.986Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.987Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:31.987Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:31.988Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:31.988Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/404.html
[2025-05-26T08:51:31.989Z] [INFO] 📝 Detected language: html
[2025-05-26T08:51:31.989Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/404.html
[2025-05-26T08:51:31.989Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\404.html
[2025-05-26T08:51:31.990Z] [INFO] 📝 Language: html
[2025-05-26T08:51:31.990Z] [INFO] 📄 Current content length: 1614
[2025-05-26T08:51:31.991Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:51:31.991Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:51:31.991Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:51:31.991Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.992Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:31.992Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:51:31.993Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:51:31.993Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:51:31.993Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:51:31.994Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.994Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:51:31.995Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:51:31.995Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:51:31.996Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:51:31.997Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/404.html
[2025-05-26T08:51:31.997Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:51:31.997Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:51:31.998Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:31.998Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:51:31.999Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:51:31.999Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:51:31.999Z] [INFO] Getting contextual info for: templates/404.html
[2025-05-26T08:51:32.096Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/404.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:51:32.305Z] [INFO] Contextual analysis completed for: templates/404.html
[2025-05-26T08:51:32.305Z] [INFO] Contextual analysis completed for templates/404.html
[2025-05-26T08:51:32.305Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:32.308Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:32.308Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:32.309Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:32.309Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:32.309Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:32.310Z] [INFO] Generating AI documentation for: templates/404.html
[2025-05-26T08:51:32.310Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:51:32.310Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:51:32.310Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:32.311Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:51:32.311Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:51:32.311Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:51:32.312Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:51:32.312Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:51:32.313Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:51:32.313Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:51:32.316Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:51:32.317Z] [WARN] Failed to generate AI documentation for: templates/404.html
[2025-05-26T08:51:32.317Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 66ce01bbefcda2729a3380907615056e
[2025-05-26T08:51:32.318Z] [INFO] 📄 File: templates/404.html
[2025-05-26T08:51:32.318Z] [INFO] 🔄 Event type: create
[2025-05-26T08:51:32.318Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:51:32.328Z] [INFO] ✅ SAVED documentation record: 66ce01bbefcda2729a3380907615056e to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\66ce01bbefcda2729a3380907615056e.json
[2025-05-26T08:51:32.335Z] [INFO] Successfully processed change: 66ce01bbefcda2729a3380907615056e
[2025-05-26T08:52:07.191Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:52:07.192Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.193Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:52:07.193Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:52:07.194Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:52:07.195Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.195Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:52:07.196Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:52:07.197Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:52:07.197Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.198Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:52:07.199Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:52:07.222Z] [INFO] Directory created: static (skipping content analysis)
[2025-05-26T08:52:07.223Z] [INFO] Directory created: static/css (skipping content analysis)
[2025-05-26T08:52:07.223Z] [INFO] File created: static/css/style.css
[2025-05-26T08:52:07.224Z] [INFO] Processing change: create - static/css/style.css
[2025-05-26T08:52:07.225Z] [INFO] Generated diff for: static/css/style.css
[2025-05-26T08:52:07.225Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for static/css/style.css
[2025-05-26T08:52:07.225Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:52:07.226Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:07.226Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:07.226Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.227Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:07.227Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:07.228Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:07.228Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:52:07.228Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:52:07.228Z] [INFO]   - Event type: create
[2025-05-26T08:52:07.229Z] [INFO]   - Has current content: true
[2025-05-26T08:52:07.229Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:07.229Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:07.229Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.230Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:07.230Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:07.231Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:07.231Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for static/css/style.css
[2025-05-26T08:52:07.231Z] [INFO] 📝 Detected language: css
[2025-05-26T08:52:07.231Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/static/css/style.css
[2025-05-26T08:52:07.232Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\static\css\style.css
[2025-05-26T08:52:07.232Z] [INFO] 📝 Language: css
[2025-05-26T08:52:07.232Z] [INFO] 📄 Current content length: 6290
[2025-05-26T08:52:07.233Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:52:07.233Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:07.233Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:07.234Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.234Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:07.235Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:07.235Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:07.236Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:52:07.236Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:52:07.237Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.238Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:52:07.238Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:52:07.239Z] [INFO] 🌐 Language css supported: false
[2025-05-26T08:52:07.239Z] [WARN] ❌ Semantic analysis not enabled for language: css
[2025-05-26T08:52:07.240Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for static/css/style.css
[2025-05-26T08:52:07.241Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:52:07.241Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:52:07.241Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.242Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:07.242Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:52:07.243Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:52:07.243Z] [INFO] Getting contextual info for: static/css/style.css
[2025-05-26T08:52:07.335Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- static/css/style.css
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:52:07.567Z] [INFO] Contextual analysis completed for: static/css/style.css
[2025-05-26T08:52:07.568Z] [INFO] Contextual analysis completed for static/css/style.css
[2025-05-26T08:52:07.568Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:52:07.568Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:52:07.569Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.570Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:52:07.570Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:52:07.570Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:52:07.571Z] [INFO] Generating AI documentation for: static/css/style.css
[2025-05-26T08:52:07.572Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:52:07.572Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:52:07.572Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.573Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:52:07.573Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:52:07.573Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:52:07.574Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:52:07.574Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:07.574Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:52:07.575Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:52:07.579Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:52:07.580Z] [WARN] Failed to generate AI documentation for: static/css/style.css
[2025-05-26T08:52:07.581Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: b28e7ddb7b8a20d5c785b980de392feb
[2025-05-26T08:52:07.581Z] [INFO] 📄 File: static/css/style.css
[2025-05-26T08:52:07.582Z] [INFO] 🔄 Event type: create
[2025-05-26T08:52:07.582Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:52:07.599Z] [INFO] ✅ SAVED documentation record: b28e7ddb7b8a20d5c785b980de392feb to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\b28e7ddb7b8a20d5c785b980de392feb.json
[2025-05-26T08:52:07.610Z] [INFO] Successfully processed change: b28e7ddb7b8a20d5c785b980de392feb
[2025-05-26T08:52:38.283Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:52:38.284Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.284Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:52:38.285Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:52:38.315Z] [INFO] File created: README.md
[2025-05-26T08:52:38.316Z] [INFO] Processing change: create - README.md
[2025-05-26T08:52:38.317Z] [INFO] Generated diff for: README.md
[2025-05-26T08:52:38.317Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for README.md
[2025-05-26T08:52:38.318Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:52:38.318Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:38.319Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:38.319Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.321Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:38.322Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:38.323Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:38.323Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:52:38.323Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:52:38.324Z] [INFO]   - Event type: create
[2025-05-26T08:52:38.324Z] [INFO]   - Has current content: true
[2025-05-26T08:52:38.324Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:38.325Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:38.325Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.326Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:38.327Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:38.327Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:38.327Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for README.md
[2025-05-26T08:52:38.328Z] [INFO] 📝 Detected language: markdown
[2025-05-26T08:52:38.328Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/README.md
[2025-05-26T08:52:38.329Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\README.md
[2025-05-26T08:52:38.329Z] [INFO] 📝 Language: markdown
[2025-05-26T08:52:38.330Z] [INFO] 📄 Current content length: 5318
[2025-05-26T08:52:38.330Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:52:38.330Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:52:38.331Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:52:38.331Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.332Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:38.333Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:52:38.333Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:52:38.333Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:52:38.334Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:52:38.334Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.335Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:52:38.336Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:52:38.337Z] [INFO] 🌐 Language markdown supported: false
[2025-05-26T08:52:38.337Z] [WARN] ❌ Semantic analysis not enabled for language: markdown
[2025-05-26T08:52:38.339Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for README.md
[2025-05-26T08:52:38.339Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:52:38.340Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:52:38.340Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.341Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:52:38.342Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:52:38.342Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:52:38.343Z] [INFO] Getting contextual info for: README.md
[2025-05-26T08:52:38.475Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- README.md
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:52:38.762Z] [INFO] Contextual analysis completed for: README.md
[2025-05-26T08:52:38.763Z] [INFO] Contextual analysis completed for README.md
[2025-05-26T08:52:38.763Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:52:38.764Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:52:38.764Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.764Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:52:38.765Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:52:38.765Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:52:38.765Z] [INFO] Generating AI documentation for: README.md
[2025-05-26T08:52:38.765Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:52:38.766Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:52:38.766Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.767Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:52:38.767Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:52:38.768Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:52:38.768Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:52:38.768Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:52:38.769Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:52:38.769Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:52:38.773Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:52:38.774Z] [WARN] Failed to generate AI documentation for: README.md
[2025-05-26T08:52:38.774Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: f123ec024538b1c44a391a014d1a4ea1
[2025-05-26T08:52:38.774Z] [INFO] 📄 File: README.md
[2025-05-26T08:52:38.775Z] [INFO] 🔄 Event type: create
[2025-05-26T08:52:38.775Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:52:38.789Z] [INFO] ✅ SAVED documentation record: f123ec024538b1c44a391a014d1a4ea1 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\f123ec024538b1c44a391a014d1a4ea1.json
[2025-05-26T08:52:38.793Z] [INFO] Successfully processed change: f123ec024538b1c44a391a014d1a4ea1
[2025-05-26T08:53:18.372Z] [INFO] 🌳 Getting record items for file: README.md
[2025-05-26T08:53:18.373Z] [INFO] 📂 LOADING RECORDS for file: README.md
[2025-05-26T08:53:18.374Z] [INFO] 📁 Found 23 files in workspace directory
[2025-05-26T08:53:18.381Z] [INFO] 📄 Found matching record: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:53:18.381Z] [INFO]   - Event type: create
[2025-05-26T08:53:18.381Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:53:18.385Z] [INFO] 📄 Found matching record: f123ec024538b1c44a391a014d1a4ea1
[2025-05-26T08:53:18.386Z] [INFO]   - Event type: create
[2025-05-26T08:53:18.386Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:53:18.387Z] [INFO] ✅ LOADED 2 records for README.md
[2025-05-26T08:53:18.388Z] [INFO] 📄 Found 2 records for README.md
[2025-05-26T08:53:18.388Z] [INFO]   Record 1: create README.md
[2025-05-26T08:53:18.389Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:53:18.389Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:53:18.389Z] [INFO]     - Collapsible state: None
[2025-05-26T08:53:18.389Z] [INFO]   Record 2: create README.md
[2025-05-26T08:53:18.390Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:53:18.390Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:53:18.390Z] [INFO]     - Collapsible state: None
[2025-05-26T08:53:22.283Z] [INFO] Showing documentation for record: f123ec024538b1c44a391a014d1a4ea1
[2025-05-26T08:53:42.393Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:53:42.394Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.395Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:53:42.395Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:53:42.396Z] [INFO] File saved: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.397Z] [INFO] Processing change: modify - c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.397Z] [INFO] Getting previous version for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.512Z] [WARN] No previous version found for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.512Z] [INFO] Generated diff for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.513Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.513Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:53:42.513Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:53:42.514Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:53:42.514Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.515Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:53:42.515Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:53:42.515Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:53:42.515Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:53:42.516Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:53:42.516Z] [INFO]   - Event type: modify
[2025-05-26T08:53:42.516Z] [INFO]   - Has current content: true
[2025-05-26T08:53:42.517Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:53:42.517Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:53:42.517Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.518Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:53:42.518Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:53:42.520Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:53:42.521Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.521Z] [INFO] 📝 Detected language: plaintext
[2025-05-26T08:53:42.521Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/c%3A/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/8ab079b2d04f1a2a3276ad345cbf9fe6/Augment.vscode-augment/Augment-Memories
[2025-05-26T08:53:42.522Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.522Z] [INFO] 📝 Language: plaintext
[2025-05-26T08:53:42.522Z] [INFO] 📄 Current content length: 67
[2025-05-26T08:53:42.523Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:53:42.523Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:53:42.523Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:53:42.523Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.524Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:53:42.524Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:53:42.525Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:53:42.525Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:53:42.525Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:53:42.525Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.526Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:53:42.527Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:53:42.527Z] [INFO] 🌐 Language plaintext supported: false
[2025-05-26T08:53:42.527Z] [WARN] ❌ Semantic analysis not enabled for language: plaintext
[2025-05-26T08:53:42.528Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.529Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:53:42.529Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:53:42.529Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.530Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:53:42.530Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:53:42.531Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:53:42.531Z] [INFO] Getting contextual info for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.612Z] [ERROR] Error finding related imports: Error: ENOENT: no such file or directory, open 'c:\Users\<USER>\Documents\augment-projects\flask_learning\c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories'
[2025-05-26T08:53:42.671Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:53:42.890Z] [INFO] Contextual analysis completed for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.891Z] [INFO] Contextual analysis completed for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.891Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:53:42.891Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:53:42.892Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.893Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:53:42.893Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:53:42.893Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:53:42.894Z] [INFO] Generating AI documentation for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.894Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:53:42.895Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:53:42.895Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.895Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:53:42.896Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:53:42.896Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:53:42.896Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:53:42.897Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:53:42.897Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:53:42.898Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:53:42.901Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:53:42.901Z] [WARN] Failed to generate AI documentation for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.902Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: d3e3fffc2756a5a2b71a95d421204a16
[2025-05-26T08:53:42.902Z] [INFO] 📄 File: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8ab079b2d04f1a2a3276ad345cbf9fe6\Augment.vscode-augment\Augment-Memories
[2025-05-26T08:53:42.903Z] [INFO] 🔄 Event type: modify
[2025-05-26T08:53:42.903Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:53:42.908Z] [INFO] ✅ SAVED documentation record: d3e3fffc2756a5a2b71a95d421204a16 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\d3e3fffc2756a5a2b71a95d421204a16.json
[2025-05-26T08:53:42.911Z] [INFO] Successfully processed change: d3e3fffc2756a5a2b71a95d421204a16
