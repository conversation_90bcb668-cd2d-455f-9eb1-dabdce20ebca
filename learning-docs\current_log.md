Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T08:17:41.256Z] [INFO] Starting workspace initialization
[2025-05-26T08:17:41.257Z] [WARN] No workspace folder found
[2025-05-26T08:17:41.257Z] [WARN] No workspace found, skipping initialization
[2025-05-26T08:17:41.258Z] [INFO] Starting change processor
[2025-05-26T08:17:41.258Z] [INFO] Starting to listen for file changes
[2025-05-26T08:17:41.259Z] [INFO] Learning Docs extension activated
Learning Docs extension deactivated
[2025-05-26T08:17:50.742Z] [INFO] Stopping change processor
[2025-05-26T08:17:50.742Z] [INFO] Stopping file change listeners
[2025-05-26T08:17:50.742Z] [INFO] Stopping file change listeners
Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T08:17:54.987Z] [INFO] Starting workspace initialization
[2025-05-26T08:17:54.988Z] [INFO] First run detected, performing initial workspace scan
[2025-05-26T08:17:54.988Z] [INFO] Starting initial workspace scan
[2025-05-26T08:17:55.063Z] [INFO] Found 0 files to baseline
[2025-05-26T08:17:55.064Z] [INFO] Initial workspace scan completed: 0 files baselined
[2025-05-26T08:17:55.068Z] [INFO] Workspace marked as initialized
[2025-05-26T08:17:55.069Z] [INFO] Workspace initialization completed
[2025-05-26T08:17:55.069Z] [INFO] Starting change processor
[2025-05-26T08:17:55.070Z] [INFO] Starting to listen for file changes
[2025-05-26T08:17:55.071Z] [INFO] Learning Docs extension activated
[2025-05-26T08:19:17.640Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:19:17.641Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.643Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:19:17.643Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:19:17.646Z] [INFO] File created: requirements.txt
[2025-05-26T08:19:17.651Z] [INFO] Processing change: create - requirements.txt
[2025-05-26T08:19:17.652Z] [INFO] Generated diff for: requirements.txt
[2025-05-26T08:19:17.653Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for requirements.txt
[2025-05-26T08:19:17.653Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:19:17.654Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:17.654Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:17.654Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.655Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:17.656Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:17.656Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:17.657Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:19:17.657Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:19:17.657Z] [INFO]   - Event type: create
[2025-05-26T08:19:17.658Z] [INFO]   - Has current content: true
[2025-05-26T08:19:17.658Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:17.658Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:17.658Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.659Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:17.660Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:17.660Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:17.661Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for requirements.txt
[2025-05-26T08:19:17.661Z] [INFO] 📝 Detected language: plaintext
[2025-05-26T08:19:17.662Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/requirements.txt
[2025-05-26T08:19:17.662Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\requirements.txt
[2025-05-26T08:19:17.662Z] [INFO] 📝 Language: plaintext
[2025-05-26T08:19:17.662Z] [INFO] 📄 Current content length: 13
[2025-05-26T08:19:17.663Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:19:17.663Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:17.663Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:17.664Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.665Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:17.665Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:17.666Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:17.666Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:19:17.666Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:19:17.667Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.668Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:19:17.668Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:19:17.668Z] [INFO] 🌐 Language plaintext supported: false
[2025-05-26T08:19:17.669Z] [WARN] ❌ Semantic analysis not enabled for language: plaintext
[2025-05-26T08:19:17.670Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for requirements.txt
[2025-05-26T08:19:17.671Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:19:17.671Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:19:17.672Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:17.672Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:17.673Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:19:17.673Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:19:17.674Z] [INFO] Getting contextual info for: requirements.txt
[2025-05-26T08:19:17.868Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- requirements.txt
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:19:18.644Z] [INFO] Contextual analysis completed for: requirements.txt
[2025-05-26T08:19:18.645Z] [INFO] Contextual analysis completed for requirements.txt
[2025-05-26T08:19:18.645Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:18.645Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:18.645Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:18.646Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:18.646Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:18.647Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:18.647Z] [INFO] Generating AI documentation for: requirements.txt
[2025-05-26T08:19:18.647Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:18.648Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:18.648Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:18.648Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:18.649Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:18.649Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:18.649Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:19:18.650Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:18.650Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:19:18.651Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:19:18.658Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:19:18.659Z] [WARN] Failed to generate AI documentation for: requirements.txt
[2025-05-26T08:19:18.659Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:19:18.660Z] [INFO] 📄 File: requirements.txt
[2025-05-26T08:19:18.660Z] [INFO] 🔄 Event type: create
[2025-05-26T08:19:18.660Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:19:18.663Z] [INFO] ✅ SAVED documentation record: 096962edf47c67c95ab6a1a08a0cc2f2 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\096962edf47c67c95ab6a1a08a0cc2f2.json
[2025-05-26T08:19:18.666Z] [INFO] Successfully processed change: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:19:39.222Z] [INFO] 🌳 Getting record items for file: requirements.txt
[2025-05-26T08:19:39.223Z] [INFO] 📂 LOADING RECORDS for file: requirements.txt
[2025-05-26T08:19:39.225Z] [INFO] 📁 Found 1 files in workspace directory
[2025-05-26T08:19:39.226Z] [INFO] 📄 Found matching record: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:19:39.226Z] [INFO]   - Event type: create
[2025-05-26T08:19:39.226Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:19:39.226Z] [INFO] ✅ LOADED 1 records for requirements.txt
[2025-05-26T08:19:39.227Z] [INFO] 📄 Found 1 records for requirements.txt
[2025-05-26T08:19:39.227Z] [INFO]   Record 1: create requirements.txt
[2025-05-26T08:19:39.227Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:19:39.228Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:19:39.228Z] [INFO]     - Collapsible state: None
[2025-05-26T08:19:40.356Z] [INFO] Showing documentation for record: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:19:41.104Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:19:41.105Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.106Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:19:41.107Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:19:41.108Z] [INFO] File created: app.py
[2025-05-26T08:19:41.114Z] [INFO] Processing change: create - app.py
[2025-05-26T08:19:41.115Z] [INFO] Generated diff for: app.py
[2025-05-26T08:19:41.116Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for app.py
[2025-05-26T08:19:41.116Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:19:41.117Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:41.117Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:41.118Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.120Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:41.121Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:41.121Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:41.122Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:19:41.122Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:19:41.123Z] [INFO]   - Event type: create
[2025-05-26T08:19:41.123Z] [INFO]   - Has current content: true
[2025-05-26T08:19:41.124Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:41.124Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:41.125Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.125Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:41.126Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:41.126Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:41.127Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for app.py
[2025-05-26T08:19:41.128Z] [INFO] 📝 Detected language: python
[2025-05-26T08:19:41.128Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/app.py
[2025-05-26T08:19:41.129Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\app.py
[2025-05-26T08:19:41.129Z] [INFO] 📝 Language: python
[2025-05-26T08:19:41.130Z] [INFO] 📄 Current content length: 4916
[2025-05-26T08:19:41.130Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:19:41.131Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:41.131Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:41.131Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.132Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:41.133Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:41.133Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:41.134Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:19:41.134Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:19:41.135Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.136Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:19:41.137Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:19:41.138Z] [INFO] 🌐 Language python supported: true
[2025-05-26T08:19:41.138Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\flask_learning\app.py
[2025-05-26T08:19:41.139Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T08:19:41.140Z] [INFO] 🔍 Getting document symbols for python (4916 chars)
[2025-05-26T08:19:41.140Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/app.py
[2025-05-26T08:19:41.140Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T08:19:41.791Z] [INFO] 📊 LSP returned 11 symbols from open document
[2025-05-26T08:19:41.792Z] [INFO] ✅ Found 11 current symbols
[2025-05-26T08:19:41.792Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T08:19:41.793Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T08:19:41.794Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T08:19:41.794Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 11
[2025-05-26T08:19:41.795Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS:
[2025-05-26T08:19:41.795Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS:
[2025-05-26T08:19:41.795Z] [INFO]   1. app (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.796Z] [INFO]   2. tasks (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.796Z] [INFO]   3. next_id (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.796Z] [INFO]   4. index (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.797Z] [INFO]     1. completed_count (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.797Z] [INFO]     2. pending_count (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.797Z] [INFO]   5. add_task (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.798Z] [INFO]     1. title (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.798Z] [INFO]     2. description (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.798Z] [INFO]     3. new_task (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.799Z] [INFO]   6. complete_task (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.799Z] [INFO]     1. task_id (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.799Z] [INFO]     2. task (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.800Z] [INFO]   7. delete_task (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.800Z] [INFO]     1. task_id (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.801Z] [INFO]     2. task (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.801Z] [INFO]   8. about (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.802Z] [INFO]   9. page_not_found (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.802Z] [INFO]     1. error (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.802Z] [INFO]   10. internal_error (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.803Z] [INFO]     1. error (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.804Z] [INFO]   11. datetime_filter (Kind: 11, Detail: 'none')
[2025-05-26T08:19:41.805Z] [INFO]     1. value (Kind: 12, Detail: 'none')
[2025-05-26T08:19:41.805Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T08:19:41.805Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.806Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T08:19:41.807Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T08:19:41.807Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T08:19:41.807Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T08:19:41.808Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 23 symbols
[2025-05-26T08:19:41.808Z] [INFO] [DIFF_SYMBOLS] 🔑 Previous keys: []
[2025-05-26T08:19:41.809Z] [INFO] [DIFF_SYMBOLS] 🔑 Current keys: [app:12, tasks:12, next_id:12, index:11, index.completed_count:12, index.pending_count:12, add_task:11, add_task.title:12, add_task.description:12, add_task.new_task:12, complete_task:11, complete_task.task_id:12, complete_task.task:12, delete_task:11, delete_task.task_id:12, delete_task.task:12, about:11, page_not_found:11, page_not_found.error:12, internal_error:11, internal_error.error:12, datetime_filter:11, datetime_filter.value:12]
[2025-05-26T08:19:41.809Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T08:19:41.810Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T08:19:41.810Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'tasks:12' -> tasks (Kind: 12)
[2025-05-26T08:19:41.810Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'next_id:12' -> next_id (Kind: 12)
[2025-05-26T08:19:41.811Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'index:11' -> index (Kind: 11)
[2025-05-26T08:19:41.811Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'index.completed_count:12' -> completed_count (Kind: 12)
[2025-05-26T08:19:41.812Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'index.pending_count:12' -> pending_count (Kind: 12)
[2025-05-26T08:19:41.812Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_task:11' -> add_task (Kind: 11)
[2025-05-26T08:19:41.812Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_task.title:12' -> title (Kind: 12)
[2025-05-26T08:19:41.813Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_task.description:12' -> description (Kind: 12)
[2025-05-26T08:19:41.813Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_task.new_task:12' -> new_task (Kind: 12)
[2025-05-26T08:19:41.813Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'complete_task:11' -> complete_task (Kind: 11)
[2025-05-26T08:19:41.814Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'complete_task.task_id:12' -> task_id (Kind: 12)
[2025-05-26T08:19:41.814Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'complete_task.task:12' -> task (Kind: 12)
[2025-05-26T08:19:41.814Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'delete_task:11' -> delete_task (Kind: 11)
[2025-05-26T08:19:41.815Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'delete_task.task_id:12' -> task_id (Kind: 12)
[2025-05-26T08:19:41.815Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'delete_task.task:12' -> task (Kind: 12)
[2025-05-26T08:19:41.815Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T08:19:41.816Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found:11' -> page_not_found (Kind: 11)
[2025-05-26T08:19:41.816Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found.error:12' -> error (Kind: 12)
[2025-05-26T08:19:41.816Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'internal_error:11' -> internal_error (Kind: 11)
[2025-05-26T08:19:41.817Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'internal_error.error:12' -> error (Kind: 12)
[2025-05-26T08:19:41.817Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'datetime_filter:11' -> datetime_filter (Kind: 11)
[2025-05-26T08:19:41.817Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'datetime_filter.value:12' -> value (Kind: 12)
[2025-05-26T08:19:41.818Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T08:19:41.818Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T08:19:41.818Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T08:19:41.819Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.820Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T08:19:41.820Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T08:19:41.821Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T08:19:41.821Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 23 primitives:
[2025-05-26T08:19:41.822Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T08:19:41.823Z] [INFO]   2. ADD variable: 'tasks'
[2025-05-26T08:19:41.823Z] [INFO]   3. ADD variable: 'next_id'
[2025-05-26T08:19:41.824Z] [INFO]   4. ADD function: 'index'
[2025-05-26T08:19:41.824Z] [INFO]   5. ADD variable: 'completed_count'
[2025-05-26T08:19:41.825Z] [INFO]   6. ADD variable: 'pending_count'
[2025-05-26T08:19:41.825Z] [INFO]   7. ADD function: 'add_task'
[2025-05-26T08:19:41.826Z] [INFO]   8. ADD variable: 'title'
[2025-05-26T08:19:41.827Z] [INFO]   9. ADD variable: 'description'
[2025-05-26T08:19:41.827Z] [INFO]   10. ADD variable: 'new_task'
[2025-05-26T08:19:41.828Z] [INFO]   11. ADD function: 'complete_task'
[2025-05-26T08:19:41.829Z] [INFO]   12. ADD variable: 'task_id'
[2025-05-26T08:19:41.830Z] [INFO]   13. ADD variable: 'task'
[2025-05-26T08:19:41.830Z] [INFO]   14. ADD function: 'delete_task'
[2025-05-26T08:19:41.831Z] [INFO]   15. ADD variable: 'task_id'
[2025-05-26T08:19:41.831Z] [INFO]   16. ADD variable: 'task'
[2025-05-26T08:19:41.832Z] [INFO]   17. ADD function: 'about'
[2025-05-26T08:19:41.832Z] [INFO]   18. ADD function: 'page_not_found'
[2025-05-26T08:19:41.832Z] [INFO]   19. ADD variable: 'error'
[2025-05-26T08:19:41.833Z] [INFO]   20. ADD function: 'internal_error'
[2025-05-26T08:19:41.833Z] [INFO]   21. ADD variable: 'error'
[2025-05-26T08:19:41.833Z] [INFO]   22. ADD function: 'datetime_filter'
[2025-05-26T08:19:41.834Z] [INFO]   23. ADD variable: 'value'
[2025-05-26T08:19:41.834Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T08:19:41.834Z] [INFO] 🎯 Generated 23 semantic primitives
[2025-05-26T08:19:41.835Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T08:19:41.836Z] [INFO]   2. ADD variable: 'tasks'
[2025-05-26T08:19:41.837Z] [INFO]   3. ADD variable: 'next_id'
[2025-05-26T08:19:41.838Z] [INFO]   4. ADD function: 'index'
[2025-05-26T08:19:41.838Z] [INFO]   5. ADD variable: 'completed_count'
[2025-05-26T08:19:41.838Z] [INFO]   6. ADD variable: 'pending_count'
[2025-05-26T08:19:41.839Z] [INFO]   7. ADD function: 'add_task'
[2025-05-26T08:19:41.839Z] [INFO]   8. ADD variable: 'title'
[2025-05-26T08:19:41.839Z] [INFO]   9. ADD variable: 'description'
[2025-05-26T08:19:41.840Z] [INFO]   10. ADD variable: 'new_task'
[2025-05-26T08:19:41.840Z] [INFO]   11. ADD function: 'complete_task'
[2025-05-26T08:19:41.840Z] [INFO]   12. ADD variable: 'task_id'
[2025-05-26T08:19:41.841Z] [INFO]   13. ADD variable: 'task'
[2025-05-26T08:19:41.841Z] [INFO]   14. ADD function: 'delete_task'
[2025-05-26T08:19:41.841Z] [INFO]   15. ADD variable: 'task_id'
[2025-05-26T08:19:41.842Z] [INFO]   16. ADD variable: 'task'
[2025-05-26T08:19:41.842Z] [INFO]   17. ADD function: 'about'
[2025-05-26T08:19:41.842Z] [INFO]   18. ADD function: 'page_not_found'
[2025-05-26T08:19:41.842Z] [INFO]   19. ADD variable: 'error'
[2025-05-26T08:19:41.843Z] [INFO]   20. ADD function: 'internal_error'
[2025-05-26T08:19:41.843Z] [INFO]   21. ADD variable: 'error'
[2025-05-26T08:19:41.843Z] [INFO]   22. ADD function: 'datetime_filter'
[2025-05-26T08:19:41.844Z] [INFO]   23. ADD variable: 'value'
[2025-05-26T08:19:41.844Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T08:19:41.844Z] [INFO] 📊 Summary: +23 -0 ~0 ↔0
[2025-05-26T08:19:41.845Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 23 primitives found
[2025-05-26T08:19:41.848Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for app.py: 23 primitives found
[2025-05-26T08:19:41.848Z] [INFO] 📊 Primitives stored in record: 23
[2025-05-26T08:19:41.849Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:19:41.849Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:19:41.849Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:41.850Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:41.851Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:19:41.851Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:19:41.851Z] [INFO] Getting contextual info for: app.py
[2025-05-26T08:19:43.177Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- app.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:19:43.376Z] [INFO] Contextual analysis completed for: app.py
[2025-05-26T08:19:43.377Z] [INFO] Contextual analysis completed for app.py
[2025-05-26T08:19:43.377Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:43.377Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:43.378Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:43.378Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:43.379Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:43.379Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:43.379Z] [INFO] Generating AI documentation for: app.py
[2025-05-26T08:19:43.379Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:43.380Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:43.380Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:43.380Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:43.381Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:43.381Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:43.381Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:19:43.382Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:43.382Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:19:43.382Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:19:43.388Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:19:43.389Z] [WARN] Failed to generate AI documentation for: app.py
[2025-05-26T08:19:43.389Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:19:43.390Z] [INFO] 📄 File: app.py
[2025-05-26T08:19:43.390Z] [INFO] 🔄 Event type: create
[2025-05-26T08:19:43.390Z] [INFO] 🔧 Semantic primitives: 23
[2025-05-26T08:19:43.390Z] [INFO]   1. add variable: 'app'
[2025-05-26T08:19:43.391Z] [INFO]   2. add variable: 'tasks'
[2025-05-26T08:19:43.391Z] [INFO]   3. add variable: 'next_id'
[2025-05-26T08:19:43.391Z] [INFO]   4. add function: 'index'
[2025-05-26T08:19:43.392Z] [INFO]   5. add variable: 'completed_count'
[2025-05-26T08:19:43.392Z] [INFO]   6. add variable: 'pending_count'
[2025-05-26T08:19:43.392Z] [INFO]   7. add function: 'add_task'
[2025-05-26T08:19:43.392Z] [INFO]   8. add variable: 'title'
[2025-05-26T08:19:43.393Z] [INFO]   9. add variable: 'description'
[2025-05-26T08:19:43.393Z] [INFO]   10. add variable: 'new_task'
[2025-05-26T08:19:43.393Z] [INFO]   11. add function: 'complete_task'
[2025-05-26T08:19:43.393Z] [INFO]   12. add variable: 'task_id'
[2025-05-26T08:19:43.394Z] [INFO]   13. add variable: 'task'
[2025-05-26T08:19:43.394Z] [INFO]   14. add function: 'delete_task'
[2025-05-26T08:19:43.394Z] [INFO]   15. add variable: 'task_id'
[2025-05-26T08:19:43.395Z] [INFO]   16. add variable: 'task'
[2025-05-26T08:19:43.395Z] [INFO]   17. add function: 'about'
[2025-05-26T08:19:43.395Z] [INFO]   18. add function: 'page_not_found'
[2025-05-26T08:19:43.396Z] [INFO]   19. add variable: 'error'
[2025-05-26T08:19:43.396Z] [INFO]   20. add function: 'internal_error'
[2025-05-26T08:19:43.396Z] [INFO]   21. add variable: 'error'
[2025-05-26T08:19:43.397Z] [INFO]   22. add function: 'datetime_filter'
[2025-05-26T08:19:43.397Z] [INFO]   23. add variable: 'value'
[2025-05-26T08:19:43.409Z] [INFO] ✅ SAVED documentation record: ca077568cb63f1b5580640f742f40173 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\ca077568cb63f1b5580640f742f40173.json
[2025-05-26T08:19:43.413Z] [INFO] Successfully processed change: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:19:55.140Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:19:55.141Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.142Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:19:55.142Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:19:55.143Z] [INFO] File created: templates
[2025-05-26T08:19:55.144Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:19:55.144Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.145Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:19:55.146Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:19:55.147Z] [INFO] File created: templates/base.html
[2025-05-26T08:19:55.155Z] [INFO] Processing change: create - templates/base.html
[2025-05-26T08:19:55.157Z] [INFO] Generated diff for: templates/base.html
[2025-05-26T08:19:55.157Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/base.html
[2025-05-26T08:19:55.158Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:19:55.158Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:55.158Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:55.159Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.160Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:55.161Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:55.161Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:55.162Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:19:55.162Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:19:55.162Z] [INFO]   - Event type: create
[2025-05-26T08:19:55.163Z] [INFO]   - Has current content: true
[2025-05-26T08:19:55.163Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:55.164Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:55.165Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.166Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:55.167Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:55.167Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:55.167Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/base.html
[2025-05-26T08:19:55.168Z] [INFO] 📝 Detected language: html
[2025-05-26T08:19:55.169Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/base.html
[2025-05-26T08:19:55.169Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\base.html
[2025-05-26T08:19:55.170Z] [INFO] 📝 Language: html
[2025-05-26T08:19:55.171Z] [INFO] 📄 Current content length: 2220
[2025-05-26T08:19:55.171Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:19:55.172Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:19:55.172Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:19:55.173Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.173Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:55.174Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:19:55.174Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:19:55.174Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:19:55.174Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:19:55.175Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.175Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:19:55.176Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:19:55.176Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:19:55.176Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:19:55.177Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/base.html
[2025-05-26T08:19:55.179Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:19:55.180Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:19:55.180Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.181Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:19:55.181Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:19:55.182Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:19:55.182Z] [INFO] Getting contextual info for: templates/base.html
[2025-05-26T08:19:55.204Z] [ERROR] Failed to process file creation: CodeExpectedError: cannot open file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates. Detail: Unable to read file 'c:\Users\<USER>\Documents\augment-projects\flask_learning\templates' (Error: Unable to read file 'c:\Users\<USER>\Documents\augment-projects\flask_learning\templates' that is actually a directory)
[2025-05-26T08:19:55.313Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/base.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:19:55.605Z] [INFO] Contextual analysis completed for: templates/base.html
[2025-05-26T08:19:55.606Z] [INFO] Contextual analysis completed for templates/base.html
[2025-05-26T08:19:55.606Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:55.607Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:55.607Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.608Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:55.608Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:55.608Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:55.609Z] [INFO] Generating AI documentation for: templates/base.html
[2025-05-26T08:19:55.609Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:19:55.610Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:19:55.610Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.611Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:19:55.611Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:19:55.611Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:19:55.611Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:19:55.612Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:19:55.612Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:19:55.612Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:19:55.616Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:19:55.616Z] [WARN] Failed to generate AI documentation for: templates/base.html
[2025-05-26T08:19:55.617Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: ed4bdfba548114b3af8c4708d6e2385b
[2025-05-26T08:19:55.617Z] [INFO] 📄 File: templates/base.html
[2025-05-26T08:19:55.617Z] [INFO] 🔄 Event type: create
[2025-05-26T08:19:55.618Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:19:55.631Z] [INFO] ✅ SAVED documentation record: ed4bdfba548114b3af8c4708d6e2385b to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\ed4bdfba548114b3af8c4708d6e2385b.json
[2025-05-26T08:19:55.641Z] [INFO] Successfully processed change: ed4bdfba548114b3af8c4708d6e2385b
[2025-05-26T08:20:09.538Z] [INFO] 🌳 Getting record items for file: requirements.txt
[2025-05-26T08:20:09.539Z] [INFO] 📂 LOADING RECORDS for file: requirements.txt
[2025-05-26T08:20:09.540Z] [INFO] 📁 Found 3 files in workspace directory
[2025-05-26T08:20:09.541Z] [INFO] 📄 Found matching record: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:20:09.541Z] [INFO]   - Event type: create
[2025-05-26T08:20:09.542Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:20:09.543Z] [INFO] ✅ LOADED 1 records for requirements.txt
[2025-05-26T08:20:09.544Z] [INFO] 📄 Found 1 records for requirements.txt
[2025-05-26T08:20:09.544Z] [INFO]   Record 1: create requirements.txt
[2025-05-26T08:20:09.544Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:20:09.545Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:20:09.545Z] [INFO]     - Collapsible state: None
[2025-05-26T08:20:10.274Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:20:10.277Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.279Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:20:10.281Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:20:10.283Z] [INFO] File created: templates/index.html
[2025-05-26T08:20:10.291Z] [INFO] Processing change: create - templates/index.html
[2025-05-26T08:20:10.292Z] [INFO] Generated diff for: templates/index.html
[2025-05-26T08:20:10.292Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/index.html
[2025-05-26T08:20:10.293Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:20:10.293Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:10.294Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:10.294Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.296Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:10.297Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:10.297Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:10.298Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:20:10.298Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:20:10.298Z] [INFO]   - Event type: create
[2025-05-26T08:20:10.299Z] [INFO]   - Has current content: true
[2025-05-26T08:20:10.299Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:10.300Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:10.300Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.301Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:10.301Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:10.303Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:10.303Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/index.html
[2025-05-26T08:20:10.304Z] [INFO] 📝 Detected language: html
[2025-05-26T08:20:10.304Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/index.html
[2025-05-26T08:20:10.305Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\index.html
[2025-05-26T08:20:10.305Z] [INFO] 📝 Language: html
[2025-05-26T08:20:10.305Z] [INFO] 📄 Current content length: 3283
[2025-05-26T08:20:10.306Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:20:10.306Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:10.306Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:10.307Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.308Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:10.308Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:10.308Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:10.309Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:20:10.309Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:20:10.310Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.310Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:20:10.311Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:20:10.311Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:20:10.312Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:20:10.313Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/index.html
[2025-05-26T08:20:10.313Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:20:10.314Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:20:10.314Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.314Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:10.315Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:20:10.315Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:20:10.315Z] [INFO] Getting contextual info for: templates/index.html
[2025-05-26T08:20:10.396Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/index.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:20:10.613Z] [INFO] Contextual analysis completed for: templates/index.html
[2025-05-26T08:20:10.613Z] [INFO] Contextual analysis completed for templates/index.html
[2025-05-26T08:20:10.613Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:10.614Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:10.614Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.614Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:10.615Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:10.615Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:10.615Z] [INFO] Generating AI documentation for: templates/index.html
[2025-05-26T08:20:10.615Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:10.616Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:10.616Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.617Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:10.617Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:10.617Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:10.618Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:20:10.618Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:10.620Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:20:10.620Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:20:10.624Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:20:10.624Z] [WARN] Failed to generate AI documentation for: templates/index.html
[2025-05-26T08:20:10.624Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: d9cf40d894fc1deb8a3852ad417ef761
[2025-05-26T08:20:10.625Z] [INFO] 📄 File: templates/index.html
[2025-05-26T08:20:10.625Z] [INFO] 🔄 Event type: create
[2025-05-26T08:20:10.625Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:20:10.634Z] [INFO] ✅ SAVED documentation record: d9cf40d894fc1deb8a3852ad417ef761 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\d9cf40d894fc1deb8a3852ad417ef761.json
[2025-05-26T08:20:10.643Z] [INFO] Successfully processed change: d9cf40d894fc1deb8a3852ad417ef761
[2025-05-26T08:20:24.839Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:20:24.839Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.840Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:20:24.841Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:20:24.841Z] [INFO] File created: templates/add_task.html
[2025-05-26T08:20:24.844Z] [INFO] Processing change: create - templates/add_task.html
[2025-05-26T08:20:24.844Z] [INFO] Generated diff for: templates/add_task.html
[2025-05-26T08:20:24.845Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/add_task.html
[2025-05-26T08:20:24.845Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:20:24.845Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:24.846Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:24.846Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.846Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:24.847Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:24.847Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:24.847Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:20:24.848Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:20:24.848Z] [INFO]   - Event type: create
[2025-05-26T08:20:24.848Z] [INFO]   - Has current content: true
[2025-05-26T08:20:24.849Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:24.849Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:24.849Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.850Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:24.850Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:24.851Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:24.851Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/add_task.html
[2025-05-26T08:20:24.852Z] [INFO] 📝 Detected language: html
[2025-05-26T08:20:24.853Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/add_task.html
[2025-05-26T08:20:24.853Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\add_task.html
[2025-05-26T08:20:24.854Z] [INFO] 📝 Language: html
[2025-05-26T08:20:24.854Z] [INFO] 📄 Current content length: 2624
[2025-05-26T08:20:24.855Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:20:24.855Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:24.855Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:24.855Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.856Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:24.856Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:24.856Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:24.857Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:20:24.857Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:20:24.857Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.858Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:20:24.858Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:20:24.859Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:20:24.859Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:20:24.860Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/add_task.html
[2025-05-26T08:20:24.860Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:20:24.861Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:20:24.861Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:24.862Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:24.862Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:20:24.862Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:20:24.863Z] [INFO] Getting contextual info for: templates/add_task.html
[2025-05-26T08:20:24.947Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/add_task.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:20:25.134Z] [INFO] Contextual analysis completed for: templates/add_task.html
[2025-05-26T08:20:25.135Z] [INFO] Contextual analysis completed for templates/add_task.html
[2025-05-26T08:20:25.136Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:25.136Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:25.136Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:25.137Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:25.138Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:25.138Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:25.138Z] [INFO] Generating AI documentation for: templates/add_task.html
[2025-05-26T08:20:25.139Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:25.139Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:25.139Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:25.140Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:25.140Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:25.140Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:25.140Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:20:25.141Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:25.141Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:20:25.141Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:20:25.144Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:20:25.145Z] [WARN] Failed to generate AI documentation for: templates/add_task.html
[2025-05-26T08:20:25.145Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 81d5cccbbe659b48ee6c35affca235b4
[2025-05-26T08:20:25.145Z] [INFO] 📄 File: templates/add_task.html
[2025-05-26T08:20:25.146Z] [INFO] 🔄 Event type: create
[2025-05-26T08:20:25.146Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:20:25.156Z] [INFO] ✅ SAVED documentation record: 81d5cccbbe659b48ee6c35affca235b4 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\81d5cccbbe659b48ee6c35affca235b4.json
[2025-05-26T08:20:25.163Z] [INFO] Successfully processed change: 81d5cccbbe659b48ee6c35affca235b4
[2025-05-26T08:20:35.573Z] [INFO] 🌳 Getting record items for file: app.py
[2025-05-26T08:20:35.574Z] [INFO] 📂 LOADING RECORDS for file: app.py
[2025-05-26T08:20:35.576Z] [INFO] 📁 Found 5 files in workspace directory
[2025-05-26T08:20:35.579Z] [INFO] 📄 Found matching record: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:20:35.579Z] [INFO]   - Event type: create
[2025-05-26T08:20:35.579Z] [INFO]   - Semantic primitives: 23
[2025-05-26T08:20:35.580Z] [INFO]     1. add variable: 'app'
[2025-05-26T08:20:35.580Z] [INFO]     2. add variable: 'tasks'
[2025-05-26T08:20:35.580Z] [INFO]     3. add variable: 'next_id'
[2025-05-26T08:20:35.580Z] [INFO]     4. add function: 'index'
[2025-05-26T08:20:35.581Z] [INFO]     5. add variable: 'completed_count'
[2025-05-26T08:20:35.581Z] [INFO]     6. add variable: 'pending_count'
[2025-05-26T08:20:35.581Z] [INFO]     7. add function: 'add_task'
[2025-05-26T08:20:35.581Z] [INFO]     8. add variable: 'title'
[2025-05-26T08:20:35.581Z] [INFO]     9. add variable: 'description'
[2025-05-26T08:20:35.582Z] [INFO]     10. add variable: 'new_task'
[2025-05-26T08:20:35.582Z] [INFO]     11. add function: 'complete_task'
[2025-05-26T08:20:35.582Z] [INFO]     12. add variable: 'task_id'
[2025-05-26T08:20:35.583Z] [INFO]     13. add variable: 'task'
[2025-05-26T08:20:35.583Z] [INFO]     14. add function: 'delete_task'
[2025-05-26T08:20:35.583Z] [INFO]     15. add variable: 'task_id'
[2025-05-26T08:20:35.583Z] [INFO]     16. add variable: 'task'
[2025-05-26T08:20:35.584Z] [INFO]     17. add function: 'about'
[2025-05-26T08:20:35.584Z] [INFO]     18. add function: 'page_not_found'
[2025-05-26T08:20:35.586Z] [INFO]     19. add variable: 'error'
[2025-05-26T08:20:35.587Z] [INFO]     20. add function: 'internal_error'
[2025-05-26T08:20:35.588Z] [INFO]     21. add variable: 'error'
[2025-05-26T08:20:35.588Z] [INFO]     22. add function: 'datetime_filter'
[2025-05-26T08:20:35.589Z] [INFO]     23. add variable: 'value'
[2025-05-26T08:20:35.594Z] [INFO] ✅ LOADED 1 records for app.py
[2025-05-26T08:20:35.594Z] [INFO] 📄 Found 1 records for app.py
[2025-05-26T08:20:35.595Z] [INFO]   Record 1: create app.py
[2025-05-26T08:20:35.595Z] [INFO]     - Has semantic primitives: true
[2025-05-26T08:20:35.595Z] [INFO]     - Semantic primitives count: 23
[2025-05-26T08:20:35.596Z] [INFO]     - Collapsible state: Collapsed
[2025-05-26T08:20:35.596Z] [INFO]       Primitive 1: add variable 'app'
[2025-05-26T08:20:35.597Z] [INFO]       Primitive 2: add variable 'tasks'
[2025-05-26T08:20:35.597Z] [INFO]       Primitive 3: add variable 'next_id'
[2025-05-26T08:20:35.598Z] [INFO]       Primitive 4: add function 'index'
[2025-05-26T08:20:35.598Z] [INFO]       Primitive 5: add variable 'completed_count'
[2025-05-26T08:20:35.598Z] [INFO]       Primitive 6: add variable 'pending_count'
[2025-05-26T08:20:35.599Z] [INFO]       Primitive 7: add function 'add_task'
[2025-05-26T08:20:35.599Z] [INFO]       Primitive 8: add variable 'title'
[2025-05-26T08:20:35.600Z] [INFO]       Primitive 9: add variable 'description'
[2025-05-26T08:20:35.600Z] [INFO]       Primitive 10: add variable 'new_task'
[2025-05-26T08:20:35.600Z] [INFO]       Primitive 11: add function 'complete_task'
[2025-05-26T08:20:35.601Z] [INFO]       Primitive 12: add variable 'task_id'
[2025-05-26T08:20:35.601Z] [INFO]       Primitive 13: add variable 'task'
[2025-05-26T08:20:35.602Z] [INFO]       Primitive 14: add function 'delete_task'
[2025-05-26T08:20:35.603Z] [INFO]       Primitive 15: add variable 'task_id'
[2025-05-26T08:20:35.604Z] [INFO]       Primitive 16: add variable 'task'
[2025-05-26T08:20:35.605Z] [INFO]       Primitive 17: add function 'about'
[2025-05-26T08:20:35.605Z] [INFO]       Primitive 18: add function 'page_not_found'
[2025-05-26T08:20:35.605Z] [INFO]       Primitive 19: add variable 'error'
[2025-05-26T08:20:35.606Z] [INFO]       Primitive 20: add function 'internal_error'
[2025-05-26T08:20:35.606Z] [INFO]       Primitive 21: add variable 'error'
[2025-05-26T08:20:35.607Z] [INFO]       Primitive 22: add function 'datetime_filter'
[2025-05-26T08:20:35.608Z] [INFO]       Primitive 23: add variable 'value'
[2025-05-26T08:20:36.603Z] [INFO] Showing documentation for record: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:20:42.106Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:20:42.107Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.107Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:20:42.108Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:20:42.108Z] [INFO] File created: templates/about.html
[2025-05-26T08:20:42.112Z] [INFO] Processing change: create - templates/about.html
[2025-05-26T08:20:42.113Z] [INFO] Generated diff for: templates/about.html
[2025-05-26T08:20:42.113Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/about.html
[2025-05-26T08:20:42.113Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:20:42.114Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:42.114Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:42.114Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.115Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:42.115Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:42.116Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:42.116Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:20:42.116Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:20:42.117Z] [INFO]   - Event type: create
[2025-05-26T08:20:42.117Z] [INFO]   - Has current content: true
[2025-05-26T08:20:42.117Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:42.117Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:42.118Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.118Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:42.119Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:42.119Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:42.120Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/about.html
[2025-05-26T08:20:42.120Z] [INFO] 📝 Detected language: html
[2025-05-26T08:20:42.120Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/about.html
[2025-05-26T08:20:42.121Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\about.html
[2025-05-26T08:20:42.121Z] [INFO] 📝 Language: html
[2025-05-26T08:20:42.121Z] [INFO] 📄 Current content length: 3390
[2025-05-26T08:20:42.122Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:20:42.122Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:42.122Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:42.122Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.123Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:42.124Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:42.124Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:42.124Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:20:42.125Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:20:42.125Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.126Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:20:42.127Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:20:42.127Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:20:42.127Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:20:42.128Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/about.html
[2025-05-26T08:20:42.129Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:20:42.129Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:20:42.129Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.130Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:42.131Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:20:42.131Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:20:42.131Z] [INFO] Getting contextual info for: templates/about.html
[2025-05-26T08:20:42.248Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/about.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:20:42.468Z] [INFO] Contextual analysis completed for: templates/about.html
[2025-05-26T08:20:42.469Z] [INFO] Contextual analysis completed for templates/about.html
[2025-05-26T08:20:42.469Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:42.470Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:42.471Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.472Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:42.472Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:42.473Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:42.473Z] [INFO] Generating AI documentation for: templates/about.html
[2025-05-26T08:20:42.473Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:42.473Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:42.474Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.474Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:42.474Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:42.475Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:42.475Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:20:42.475Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:42.476Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:20:42.476Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:20:42.479Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:20:42.480Z] [WARN] Failed to generate AI documentation for: templates/about.html
[2025-05-26T08:20:42.480Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 44f91c8aedbf07c89e232eb33889f960
[2025-05-26T08:20:42.480Z] [INFO] 📄 File: templates/about.html
[2025-05-26T08:20:42.481Z] [INFO] 🔄 Event type: create
[2025-05-26T08:20:42.481Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:20:42.491Z] [INFO] ✅ SAVED documentation record: 44f91c8aedbf07c89e232eb33889f960 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\44f91c8aedbf07c89e232eb33889f960.json
[2025-05-26T08:20:42.499Z] [INFO] Successfully processed change: 44f91c8aedbf07c89e232eb33889f960
[2025-05-26T08:20:51.291Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:20:51.292Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.293Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:20:51.294Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:20:51.294Z] [INFO] File created: templates/404.html
[2025-05-26T08:20:51.297Z] [INFO] Processing change: create - templates/404.html
[2025-05-26T08:20:51.298Z] [INFO] Generated diff for: templates/404.html
[2025-05-26T08:20:51.298Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/404.html
[2025-05-26T08:20:51.298Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:20:51.298Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:51.299Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:51.299Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.300Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:51.300Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:51.300Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:51.301Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:20:51.301Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:20:51.302Z] [INFO]   - Event type: create
[2025-05-26T08:20:51.302Z] [INFO]   - Has current content: true
[2025-05-26T08:20:51.303Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:51.303Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:51.304Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.305Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:51.305Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:51.305Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:51.306Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/404.html
[2025-05-26T08:20:51.306Z] [INFO] 📝 Detected language: html
[2025-05-26T08:20:51.306Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/404.html
[2025-05-26T08:20:51.306Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\404.html
[2025-05-26T08:20:51.307Z] [INFO] 📝 Language: html
[2025-05-26T08:20:51.307Z] [INFO] 📄 Current content length: 1046
[2025-05-26T08:20:51.307Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:20:51.307Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:51.308Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:51.308Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.309Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:51.309Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:51.309Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:51.309Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:20:51.310Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:20:51.310Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.311Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:20:51.311Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:20:51.311Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:20:51.312Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:20:51.313Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/404.html
[2025-05-26T08:20:51.313Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:20:51.313Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:20:51.314Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.315Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:51.315Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:20:51.315Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:20:51.315Z] [INFO] Getting contextual info for: templates/404.html
[2025-05-26T08:20:51.407Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/404.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:20:51.666Z] [INFO] Contextual analysis completed for: templates/404.html
[2025-05-26T08:20:51.667Z] [INFO] Contextual analysis completed for templates/404.html
[2025-05-26T08:20:51.667Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:51.667Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:51.667Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.668Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:51.668Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:51.669Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:51.669Z] [INFO] Generating AI documentation for: templates/404.html
[2025-05-26T08:20:51.670Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:51.670Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:51.670Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.671Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:51.671Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:51.672Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:51.672Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:20:51.672Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:51.673Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:20:51.673Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:20:51.676Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:20:51.676Z] [WARN] Failed to generate AI documentation for: templates/404.html
[2025-05-26T08:20:51.677Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 6a2118e987fba622d0c25051a943394c
[2025-05-26T08:20:51.677Z] [INFO] 📄 File: templates/404.html
[2025-05-26T08:20:51.677Z] [INFO] 🔄 Event type: create
[2025-05-26T08:20:51.678Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:20:51.687Z] [INFO] ✅ SAVED documentation record: 6a2118e987fba622d0c25051a943394c to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\6a2118e987fba622d0c25051a943394c.json
[2025-05-26T08:20:51.694Z] [INFO] Successfully processed change: 6a2118e987fba622d0c25051a943394c
[2025-05-26T08:20:58.338Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:20:58.339Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.339Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:20:58.340Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:20:58.341Z] [INFO] File created: templates/500.html
[2025-05-26T08:20:58.344Z] [INFO] Processing change: create - templates/500.html
[2025-05-26T08:20:58.344Z] [INFO] Generated diff for: templates/500.html
[2025-05-26T08:20:58.345Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/500.html
[2025-05-26T08:20:58.345Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:20:58.345Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:58.345Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:58.346Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.346Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:58.346Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:58.347Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:58.347Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:20:58.347Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:20:58.348Z] [INFO]   - Event type: create
[2025-05-26T08:20:58.348Z] [INFO]   - Has current content: true
[2025-05-26T08:20:58.348Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:58.348Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:58.348Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.349Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:58.349Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:58.350Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:58.350Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/500.html
[2025-05-26T08:20:58.350Z] [INFO] 📝 Detected language: html
[2025-05-26T08:20:58.350Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/templates/500.html
[2025-05-26T08:20:58.351Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\templates\500.html
[2025-05-26T08:20:58.351Z] [INFO] 📝 Language: html
[2025-05-26T08:20:58.351Z] [INFO] 📄 Current content length: 958
[2025-05-26T08:20:58.353Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:20:58.353Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:20:58.354Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:20:58.355Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.355Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:58.356Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:20:58.356Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:20:58.356Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:20:58.357Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:20:58.357Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.358Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:20:58.358Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:20:58.359Z] [INFO] 🌐 Language html supported: false
[2025-05-26T08:20:58.359Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T08:20:58.361Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/500.html
[2025-05-26T08:20:58.361Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:20:58.361Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:20:58.362Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.363Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:20:58.363Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:20:58.364Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:20:58.365Z] [INFO] Getting contextual info for: templates/500.html
[2025-05-26T08:20:58.465Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/500.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:20:58.669Z] [INFO] Contextual analysis completed for: templates/500.html
[2025-05-26T08:20:58.669Z] [INFO] Contextual analysis completed for templates/500.html
[2025-05-26T08:20:58.670Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:58.670Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:58.670Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.671Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:58.671Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:58.672Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:58.672Z] [INFO] Generating AI documentation for: templates/500.html
[2025-05-26T08:20:58.672Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:20:58.672Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:20:58.673Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.673Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:20:58.674Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:20:58.674Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:20:58.674Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:20:58.675Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:20:58.676Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:20:58.676Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:20:58.681Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:20:58.682Z] [WARN] Failed to generate AI documentation for: templates/500.html
[2025-05-26T08:20:58.683Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 5fec7e67000c816593fe33667d79986f
[2025-05-26T08:20:58.683Z] [INFO] 📄 File: templates/500.html
[2025-05-26T08:20:58.683Z] [INFO] 🔄 Event type: create
[2025-05-26T08:20:58.684Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:20:58.694Z] [INFO] ✅ SAVED documentation record: 5fec7e67000c816593fe33667d79986f to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\5fec7e67000c816593fe33667d79986f.json
[2025-05-26T08:20:58.704Z] [INFO] Successfully processed change: 5fec7e67000c816593fe33667d79986f
[2025-05-26T08:21:16.555Z] [INFO] 🌳 Getting record items for file: app.py
[2025-05-26T08:21:16.556Z] [INFO] 📂 LOADING RECORDS for file: app.py
[2025-05-26T08:21:16.557Z] [INFO] 📁 Found 8 files in workspace directory
[2025-05-26T08:21:16.561Z] [INFO] 📄 Found matching record: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:21:16.561Z] [INFO]   - Event type: create
[2025-05-26T08:21:16.561Z] [INFO]   - Semantic primitives: 23
[2025-05-26T08:21:16.561Z] [INFO]     1. add variable: 'app'
[2025-05-26T08:21:16.562Z] [INFO]     2. add variable: 'tasks'
[2025-05-26T08:21:16.562Z] [INFO]     3. add variable: 'next_id'
[2025-05-26T08:21:16.562Z] [INFO]     4. add function: 'index'
[2025-05-26T08:21:16.562Z] [INFO]     5. add variable: 'completed_count'
[2025-05-26T08:21:16.563Z] [INFO]     6. add variable: 'pending_count'
[2025-05-26T08:21:16.563Z] [INFO]     7. add function: 'add_task'
[2025-05-26T08:21:16.563Z] [INFO]     8. add variable: 'title'
[2025-05-26T08:21:16.563Z] [INFO]     9. add variable: 'description'
[2025-05-26T08:21:16.564Z] [INFO]     10. add variable: 'new_task'
[2025-05-26T08:21:16.564Z] [INFO]     11. add function: 'complete_task'
[2025-05-26T08:21:16.564Z] [INFO]     12. add variable: 'task_id'
[2025-05-26T08:21:16.564Z] [INFO]     13. add variable: 'task'
[2025-05-26T08:21:16.565Z] [INFO]     14. add function: 'delete_task'
[2025-05-26T08:21:16.565Z] [INFO]     15. add variable: 'task_id'
[2025-05-26T08:21:16.565Z] [INFO]     16. add variable: 'task'
[2025-05-26T08:21:16.565Z] [INFO]     17. add function: 'about'
[2025-05-26T08:21:16.566Z] [INFO]     18. add function: 'page_not_found'
[2025-05-26T08:21:16.566Z] [INFO]     19. add variable: 'error'
[2025-05-26T08:21:16.566Z] [INFO]     20. add function: 'internal_error'
[2025-05-26T08:21:16.566Z] [INFO]     21. add variable: 'error'
[2025-05-26T08:21:16.567Z] [INFO]     22. add function: 'datetime_filter'
[2025-05-26T08:21:16.567Z] [INFO]     23. add variable: 'value'
[2025-05-26T08:21:16.570Z] [INFO] ✅ LOADED 1 records for app.py
[2025-05-26T08:21:16.571Z] [INFO] 📄 Found 1 records for app.py
[2025-05-26T08:21:16.571Z] [INFO]   Record 1: create app.py
[2025-05-26T08:21:16.571Z] [INFO]     - Has semantic primitives: true
[2025-05-26T08:21:16.572Z] [INFO]     - Semantic primitives count: 23
[2025-05-26T08:21:16.572Z] [INFO]     - Collapsible state: Collapsed
[2025-05-26T08:21:16.572Z] [INFO]       Primitive 1: add variable 'app'
[2025-05-26T08:21:16.572Z] [INFO]       Primitive 2: add variable 'tasks'
[2025-05-26T08:21:16.573Z] [INFO]       Primitive 3: add variable 'next_id'
[2025-05-26T08:21:16.573Z] [INFO]       Primitive 4: add function 'index'
[2025-05-26T08:21:16.573Z] [INFO]       Primitive 5: add variable 'completed_count'
[2025-05-26T08:21:16.573Z] [INFO]       Primitive 6: add variable 'pending_count'
[2025-05-26T08:21:16.574Z] [INFO]       Primitive 7: add function 'add_task'
[2025-05-26T08:21:16.574Z] [INFO]       Primitive 8: add variable 'title'
[2025-05-26T08:21:16.575Z] [INFO]       Primitive 9: add variable 'description'
[2025-05-26T08:21:16.575Z] [INFO]       Primitive 10: add variable 'new_task'
[2025-05-26T08:21:16.575Z] [INFO]       Primitive 11: add function 'complete_task'
[2025-05-26T08:21:16.576Z] [INFO]       Primitive 12: add variable 'task_id'
[2025-05-26T08:21:16.576Z] [INFO]       Primitive 13: add variable 'task'
[2025-05-26T08:21:16.576Z] [INFO]       Primitive 14: add function 'delete_task'
[2025-05-26T08:21:16.576Z] [INFO]       Primitive 15: add variable 'task_id'
[2025-05-26T08:21:16.577Z] [INFO]       Primitive 16: add variable 'task'
[2025-05-26T08:21:16.577Z] [INFO]       Primitive 17: add function 'about'
[2025-05-26T08:21:16.577Z] [INFO]       Primitive 18: add function 'page_not_found'
[2025-05-26T08:21:16.577Z] [INFO]       Primitive 19: add variable 'error'
[2025-05-26T08:21:16.578Z] [INFO]       Primitive 20: add function 'internal_error'
[2025-05-26T08:21:16.578Z] [INFO]       Primitive 21: add variable 'error'
[2025-05-26T08:21:16.578Z] [INFO]       Primitive 22: add function 'datetime_filter'
[2025-05-26T08:21:16.578Z] [INFO]       Primitive 23: add variable 'value'
[2025-05-26T08:21:16.579Z] [INFO] 🌳 Getting record items for file: requirements.txt
[2025-05-26T08:21:16.579Z] [INFO] 📂 LOADING RECORDS for file: requirements.txt
[2025-05-26T08:21:16.581Z] [INFO] 📁 Found 8 files in workspace directory
[2025-05-26T08:21:16.582Z] [INFO] 📄 Found matching record: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:21:16.582Z] [INFO]   - Event type: create
[2025-05-26T08:21:16.583Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:21:16.587Z] [INFO] ✅ LOADED 1 records for requirements.txt
[2025-05-26T08:21:16.588Z] [INFO] 📄 Found 1 records for requirements.txt
[2025-05-26T08:21:16.588Z] [INFO]   Record 1: create requirements.txt
[2025-05-26T08:21:16.588Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:21:16.589Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:21:16.589Z] [INFO]     - Collapsible state: None
[2025-05-26T08:21:18.605Z] [INFO] 🌳 Getting record items for file: templates/404.html
[2025-05-26T08:21:18.606Z] [INFO] 📂 LOADING RECORDS for file: templates/404.html
[2025-05-26T08:21:18.608Z] [INFO] 📁 Found 8 files in workspace directory
[2025-05-26T08:21:18.610Z] [INFO] 📄 Found matching record: 6a2118e987fba622d0c25051a943394c
[2025-05-26T08:21:18.611Z] [INFO]   - Event type: create
[2025-05-26T08:21:18.611Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:21:18.613Z] [INFO] ✅ LOADED 1 records for templates/404.html
[2025-05-26T08:21:18.614Z] [INFO] 📄 Found 1 records for templates/404.html
[2025-05-26T08:21:18.614Z] [INFO]   Record 1: create templates/404.html
[2025-05-26T08:21:18.614Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:21:18.614Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:21:18.615Z] [INFO]     - Collapsible state: None
[2025-05-26T08:21:19.666Z] [INFO] Showing documentation for record: 6a2118e987fba622d0c25051a943394c
[2025-05-26T08:21:46.692Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:21:46.692Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.693Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:21:46.694Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:21:46.695Z] [INFO] File created: static
[2025-05-26T08:21:46.696Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:21:46.696Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.697Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:21:46.697Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:21:46.698Z] [INFO] File created: static/style.css
[2025-05-26T08:21:46.704Z] [INFO] Processing change: create - static/style.css
[2025-05-26T08:21:46.707Z] [INFO] Generated diff for: static/style.css
[2025-05-26T08:21:46.707Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for static/style.css
[2025-05-26T08:21:46.708Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:21:46.708Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:21:46.708Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:21:46.709Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.710Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:21:46.710Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:21:46.711Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:21:46.711Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:21:46.711Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:21:46.711Z] [INFO]   - Event type: create
[2025-05-26T08:21:46.712Z] [INFO]   - Has current content: true
[2025-05-26T08:21:46.712Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:21:46.712Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:21:46.712Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.713Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:21:46.713Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:21:46.713Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:21:46.714Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for static/style.css
[2025-05-26T08:21:46.714Z] [INFO] 📝 Detected language: css
[2025-05-26T08:21:46.714Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/static/style.css
[2025-05-26T08:21:46.715Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\static\style.css
[2025-05-26T08:21:46.715Z] [INFO] 📝 Language: css
[2025-05-26T08:21:46.715Z] [INFO] 📄 Current content length: 8653
[2025-05-26T08:21:46.716Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:21:46.716Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:21:46.716Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:21:46.717Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.717Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:21:46.718Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:21:46.718Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:21:46.719Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:21:46.721Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:21:46.722Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.722Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:21:46.723Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:21:46.723Z] [INFO] 🌐 Language css supported: false
[2025-05-26T08:21:46.724Z] [WARN] ❌ Semantic analysis not enabled for language: css
[2025-05-26T08:21:46.724Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for static/style.css
[2025-05-26T08:21:46.725Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:21:46.725Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:21:46.725Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:46.726Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:21:46.726Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:21:46.727Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:21:46.727Z] [INFO] Getting contextual info for: static/style.css
[2025-05-26T08:21:46.744Z] [ERROR] Failed to process file creation: CodeExpectedError: cannot open file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/static. Detail: Unable to read file 'c:\Users\<USER>\Documents\augment-projects\flask_learning\static' (Error: Unable to read file 'c:\Users\<USER>\Documents\augment-projects\flask_learning\static' that is actually a directory)
[2025-05-26T08:21:46.852Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- static/style.css
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:21:47.117Z] [INFO] Contextual analysis completed for: static/style.css
[2025-05-26T08:21:47.118Z] [INFO] Contextual analysis completed for static/style.css
[2025-05-26T08:21:47.119Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:21:47.121Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:21:47.122Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:47.123Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:21:47.124Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:21:47.125Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:21:47.126Z] [INFO] Generating AI documentation for: static/style.css
[2025-05-26T08:21:47.126Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:21:47.127Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:21:47.127Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:47.128Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:21:47.129Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:21:47.129Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:21:47.130Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:21:47.130Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:21:47.130Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:21:47.131Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:21:47.135Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:21:47.135Z] [WARN] Failed to generate AI documentation for: static/style.css
[2025-05-26T08:21:47.136Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 1a411641bd8a2d10b14538ecce8aeddd
[2025-05-26T08:21:47.136Z] [INFO] 📄 File: static/style.css
[2025-05-26T08:21:47.137Z] [INFO] 🔄 Event type: create
[2025-05-26T08:21:47.137Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:21:47.149Z] [INFO] ✅ SAVED documentation record: 1a411641bd8a2d10b14538ecce8aeddd to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\1a411641bd8a2d10b14538ecce8aeddd.json
[2025-05-26T08:21:47.156Z] [INFO] Successfully processed change: 1a411641bd8a2d10b14538ecce8aeddd
[2025-05-26T08:22:19.101Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T08:22:19.103Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.104Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T08:22:19.104Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T08:22:19.105Z] [INFO] File created: README.md
[2025-05-26T08:22:19.110Z] [INFO] Processing change: create - README.md
[2025-05-26T08:22:19.111Z] [INFO] Generated diff for: README.md
[2025-05-26T08:22:19.112Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for README.md
[2025-05-26T08:22:19.112Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T08:22:19.112Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:22:19.113Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:22:19.113Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.114Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:22:19.115Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:22:19.115Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:22:19.115Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T08:22:19.116Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T08:22:19.116Z] [INFO]   - Event type: create
[2025-05-26T08:22:19.117Z] [INFO]   - Has current content: true
[2025-05-26T08:22:19.117Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:22:19.117Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:22:19.118Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.119Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:22:19.120Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:22:19.120Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:22:19.121Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for README.md
[2025-05-26T08:22:19.121Z] [INFO] 📝 Detected language: markdown
[2025-05-26T08:22:19.122Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/flask_learning/README.md
[2025-05-26T08:22:19.122Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\flask_learning\README.md
[2025-05-26T08:22:19.123Z] [INFO] 📝 Language: markdown
[2025-05-26T08:22:19.123Z] [INFO] 📄 Current content length: 5396
[2025-05-26T08:22:19.123Z] [INFO] 📄 Previous content length: 0
[2025-05-26T08:22:19.124Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T08:22:19.124Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T08:22:19.124Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.125Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T08:22:19.125Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T08:22:19.125Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T08:22:19.126Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T08:22:19.126Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T08:22:19.126Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.127Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T08:22:19.128Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T08:22:19.128Z] [INFO] 🌐 Language markdown supported: false
[2025-05-26T08:22:19.129Z] [WARN] ❌ Semantic analysis not enabled for language: markdown
[2025-05-26T08:22:19.130Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for README.md
[2025-05-26T08:22:19.130Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T08:22:19.130Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T08:22:19.133Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.133Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T08:22:19.134Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T08:22:19.134Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T08:22:19.134Z] [INFO] Getting contextual info for: README.md
[2025-05-26T08:22:19.273Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- README.md
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T08:22:19.571Z] [INFO] Contextual analysis completed for: README.md
[2025-05-26T08:22:19.573Z] [INFO] Contextual analysis completed for README.md
[2025-05-26T08:22:19.575Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:22:19.576Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:22:19.577Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.579Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:22:19.580Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:22:19.581Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:22:19.582Z] [INFO] Generating AI documentation for: README.md
[2025-05-26T08:22:19.582Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T08:22:19.583Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T08:22:19.584Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.586Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T08:22:19.587Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T08:22:19.587Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T08:22:19.588Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T08:22:19.589Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T08:22:19.589Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T08:22:19.590Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T08:22:19.594Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T08:22:19.595Z] [WARN] Failed to generate AI documentation for: README.md
[2025-05-26T08:22:19.595Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:22:19.595Z] [INFO] 📄 File: README.md
[2025-05-26T08:22:19.596Z] [INFO] 🔄 Event type: create
[2025-05-26T08:22:19.596Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T08:22:19.604Z] [INFO] ✅ SAVED documentation record: 94c3469d685a8a3d23fa17aef68e3307 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\flask_learning\94c3469d685a8a3d23fa17aef68e3307.json
[2025-05-26T08:22:19.608Z] [INFO] Successfully processed change: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:22:23.908Z] [INFO] 🌳 Getting record items for file: app.py
[2025-05-26T08:22:23.908Z] [INFO] 📂 LOADING RECORDS for file: app.py
[2025-05-26T08:22:23.910Z] [INFO] 📁 Found 10 files in workspace directory
[2025-05-26T08:22:23.914Z] [INFO] 📄 Found matching record: ca077568cb63f1b5580640f742f40173
[2025-05-26T08:22:23.915Z] [INFO]   - Event type: create
[2025-05-26T08:22:23.915Z] [INFO]   - Semantic primitives: 23
[2025-05-26T08:22:23.915Z] [INFO]     1. add variable: 'app'
[2025-05-26T08:22:23.916Z] [INFO]     2. add variable: 'tasks'
[2025-05-26T08:22:23.916Z] [INFO]     3. add variable: 'next_id'
[2025-05-26T08:22:23.917Z] [INFO]     4. add function: 'index'
[2025-05-26T08:22:23.917Z] [INFO]     5. add variable: 'completed_count'
[2025-05-26T08:22:23.917Z] [INFO]     6. add variable: 'pending_count'
[2025-05-26T08:22:23.918Z] [INFO]     7. add function: 'add_task'
[2025-05-26T08:22:23.918Z] [INFO]     8. add variable: 'title'
[2025-05-26T08:22:23.919Z] [INFO]     9. add variable: 'description'
[2025-05-26T08:22:23.919Z] [INFO]     10. add variable: 'new_task'
[2025-05-26T08:22:23.920Z] [INFO]     11. add function: 'complete_task'
[2025-05-26T08:22:23.920Z] [INFO]     12. add variable: 'task_id'
[2025-05-26T08:22:23.920Z] [INFO]     13. add variable: 'task'
[2025-05-26T08:22:23.921Z] [INFO]     14. add function: 'delete_task'
[2025-05-26T08:22:23.921Z] [INFO]     15. add variable: 'task_id'
[2025-05-26T08:22:23.922Z] [INFO]     16. add variable: 'task'
[2025-05-26T08:22:23.922Z] [INFO]     17. add function: 'about'
[2025-05-26T08:22:23.922Z] [INFO]     18. add function: 'page_not_found'
[2025-05-26T08:22:23.922Z] [INFO]     19. add variable: 'error'
[2025-05-26T08:22:23.923Z] [INFO]     20. add function: 'internal_error'
[2025-05-26T08:22:23.923Z] [INFO]     21. add variable: 'error'
[2025-05-26T08:22:23.923Z] [INFO]     22. add function: 'datetime_filter'
[2025-05-26T08:22:23.924Z] [INFO]     23. add variable: 'value'
[2025-05-26T08:22:23.927Z] [INFO] ✅ LOADED 1 records for app.py
[2025-05-26T08:22:23.928Z] [INFO] 📄 Found 1 records for app.py
[2025-05-26T08:22:23.928Z] [INFO]   Record 1: create app.py
[2025-05-26T08:22:23.928Z] [INFO]     - Has semantic primitives: true
[2025-05-26T08:22:23.929Z] [INFO]     - Semantic primitives count: 23
[2025-05-26T08:22:23.929Z] [INFO]     - Collapsible state: Collapsed
[2025-05-26T08:22:23.929Z] [INFO]       Primitive 1: add variable 'app'
[2025-05-26T08:22:23.929Z] [INFO]       Primitive 2: add variable 'tasks'
[2025-05-26T08:22:23.930Z] [INFO]       Primitive 3: add variable 'next_id'
[2025-05-26T08:22:23.930Z] [INFO]       Primitive 4: add function 'index'
[2025-05-26T08:22:23.930Z] [INFO]       Primitive 5: add variable 'completed_count'
[2025-05-26T08:22:23.930Z] [INFO]       Primitive 6: add variable 'pending_count'
[2025-05-26T08:22:23.931Z] [INFO]       Primitive 7: add function 'add_task'
[2025-05-26T08:22:23.931Z] [INFO]       Primitive 8: add variable 'title'
[2025-05-26T08:22:23.931Z] [INFO]       Primitive 9: add variable 'description'
[2025-05-26T08:22:23.932Z] [INFO]       Primitive 10: add variable 'new_task'
[2025-05-26T08:22:23.932Z] [INFO]       Primitive 11: add function 'complete_task'
[2025-05-26T08:22:23.932Z] [INFO]       Primitive 12: add variable 'task_id'
[2025-05-26T08:22:23.932Z] [INFO]       Primitive 13: add variable 'task'
[2025-05-26T08:22:23.933Z] [INFO]       Primitive 14: add function 'delete_task'
[2025-05-26T08:22:23.933Z] [INFO]       Primitive 15: add variable 'task_id'
[2025-05-26T08:22:23.933Z] [INFO]       Primitive 16: add variable 'task'
[2025-05-26T08:22:23.933Z] [INFO]       Primitive 17: add function 'about'
[2025-05-26T08:22:23.934Z] [INFO]       Primitive 18: add function 'page_not_found'
[2025-05-26T08:22:23.934Z] [INFO]       Primitive 19: add variable 'error'
[2025-05-26T08:22:23.934Z] [INFO]       Primitive 20: add function 'internal_error'
[2025-05-26T08:22:23.935Z] [INFO]       Primitive 21: add variable 'error'
[2025-05-26T08:22:23.935Z] [INFO]       Primitive 22: add function 'datetime_filter'
[2025-05-26T08:22:23.935Z] [INFO]       Primitive 23: add variable 'value'
[2025-05-26T08:22:23.937Z] [INFO] 🌳 Getting record items for file: requirements.txt
[2025-05-26T08:22:23.938Z] [INFO] 📂 LOADING RECORDS for file: requirements.txt
[2025-05-26T08:22:23.940Z] [INFO] 📁 Found 10 files in workspace directory
[2025-05-26T08:22:23.943Z] [INFO] 📄 Found matching record: 096962edf47c67c95ab6a1a08a0cc2f2
[2025-05-26T08:22:23.944Z] [INFO]   - Event type: create
[2025-05-26T08:22:23.944Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:22:23.949Z] [INFO] ✅ LOADED 1 records for requirements.txt
[2025-05-26T08:22:23.949Z] [INFO] 📄 Found 1 records for requirements.txt
[2025-05-26T08:22:23.949Z] [INFO]   Record 1: create requirements.txt
[2025-05-26T08:22:23.950Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:22:23.950Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:22:23.950Z] [INFO]     - Collapsible state: None
[2025-05-26T08:22:23.951Z] [INFO] 🌳 Getting record items for file: templates/404.html
[2025-05-26T08:22:23.951Z] [INFO] 📂 LOADING RECORDS for file: templates/404.html
[2025-05-26T08:22:23.955Z] [INFO] 📁 Found 10 files in workspace directory
[2025-05-26T08:22:23.959Z] [INFO] 📄 Found matching record: 6a2118e987fba622d0c25051a943394c
[2025-05-26T08:22:23.959Z] [INFO]   - Event type: create
[2025-05-26T08:22:23.959Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:22:23.962Z] [INFO] ✅ LOADED 1 records for templates/404.html
[2025-05-26T08:22:23.962Z] [INFO] 📄 Found 1 records for templates/404.html
[2025-05-26T08:22:23.963Z] [INFO]   Record 1: create templates/404.html
[2025-05-26T08:22:23.963Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:22:23.963Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:22:23.963Z] [INFO]     - Collapsible state: None
[2025-05-26T08:22:26.806Z] [INFO] 🌳 Getting record items for file: README.md
[2025-05-26T08:22:26.807Z] [INFO] 📂 LOADING RECORDS for file: README.md
[2025-05-26T08:22:26.808Z] [INFO] 📁 Found 10 files in workspace directory
[2025-05-26T08:22:26.814Z] [INFO] 📄 Found matching record: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:22:26.814Z] [INFO]   - Event type: create
[2025-05-26T08:22:26.814Z] [INFO]   - Semantic primitives: 0
[2025-05-26T08:22:26.816Z] [INFO] ✅ LOADED 1 records for README.md
[2025-05-26T08:22:26.817Z] [INFO] 📄 Found 1 records for README.md
[2025-05-26T08:22:26.817Z] [INFO]   Record 1: create README.md
[2025-05-26T08:22:26.817Z] [INFO]     - Has semantic primitives: undefined
[2025-05-26T08:22:26.818Z] [INFO]     - Semantic primitives count: 0
[2025-05-26T08:22:26.818Z] [INFO]     - Collapsible state: None
[2025-05-26T08:22:27.787Z] [INFO] Showing documentation for record: 94c3469d685a8a3d23fa17aef68e3307
[2025-05-26T08:22:35.235Z] [INFO] Showing documentation for record: ca077568cb63f1b5580640f742f40173
