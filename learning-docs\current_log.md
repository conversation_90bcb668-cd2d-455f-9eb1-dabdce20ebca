Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T09:07:06.749Z] [INFO] Starting workspace initialization
[2025-05-26T09:07:06.749Z] [WARN] No workspace folder found
[2025-05-26T09:07:06.749Z] [WARN] No workspace found, skipping initialization
[2025-05-26T09:07:06.750Z] [INFO] Starting change processor
[2025-05-26T09:07:06.751Z] [INFO] Starting to listen for file changes
[2025-05-26T09:07:06.752Z] [INFO] Learning Docs extension activated
[2025-05-26T09:07:07.353Z] [WARN] No workspace folder found
[2025-05-26T09:07:13.927Z] [WARN] No workspace folder found
Learning Docs extension deactivated
[2025-05-26T09:07:28.456Z] [INFO] Stopping change processor
[2025-05-26T09:07:28.456Z] [INFO] Stopping file change listeners
[2025-05-26T09:07:28.457Z] [INFO] Stopping file change listeners
[2025-05-26T09:07:31.131Z] [INFO] Starting workspace initialization
[2025-05-26T09:07:31.133Z] [INFO] First run detected, performing initial workspace scan
[2025-05-26T09:07:31.133Z] [INFO] Starting initial workspace scan
Registered account method: accountLoginClicked
Registered account method: accountLogoutClicked
Registered browser method: discoverBrowser
Registered browser method: getBrowserConnectionInfo
Registered browser method: getDetectedChromePath
Registered browser method: relaunchChromeDebugMode
Registered browser method: testBrowserConnection
Registered browser method: updateBrowserSettings
Registered checkpoints method: checkpointDiff
Registered checkpoints method: checkpointRestore
Registered file method: copyToClipboard
Registered file method: createRuleFile
Registered file method: deleteRuleFile
Registered file method: getRelativePaths
Registered file method: openFile
Registered file method: openImage
Registered file method: openMention
Registered file method: refreshRules
Registered file method: searchCommits
Registered file method: searchFiles
Registered file method: selectImages
Registered file method: toggleClineRule
Registered file method: toggleCursorRule
Registered file method: toggleWindsurfRule
Registered mcp method: addRemoteMcpServer
Registered mcp method: deleteMcpServer
Registered mcp method: downloadMcp
Registered mcp method: refreshMcpMarketplace
Registered mcp method: restartMcpServer
Registered mcp method: toggleMcpServer
Registered mcp method: toggleToolAutoApprove
Registered mcp method: updateMcpTimeout
Registered state method: getLatestState
Registered state method: resetState
Registered state method: subscribeToState (streaming)
Registered state method: toggleFavoriteModel
Registered state method: togglePlanActMode
Registered state method: updateAutoApprovalSettings
Registered state method: updateTerminalConnectionTimeout
Registered task method: askResponse
Registered task method: cancelTask
Registered task method: clearTask
Registered task method: deleteNonFavoritedTasks
Registered task method: deleteTasksWithIds
Registered task method: exportTaskWithId
Registered task method: getTaskHistory
Registered task method: newTask
Registered task method: showTaskWithId
Registered task method: taskCompletionViewChanges
Registered task method: taskFeedback
Registered task method: toggleTaskFavorite
Registered web method: checkIsImageUrl
Registered web method: fetchOpenGraphData
Registered models method: getLmStudioModels
Registered models method: getOllamaModels
Registered models method: getVsCodeLmModels
Registered models method: refreshOpenAiModels
Registered models method: refreshOpenRouterModels
Registered models method: refreshRequestyModels
Registered slash method: condense
Registered slash method: reportBug
Registered ui method: onDidShowAnnouncement
Registered ui method: scrollToSettings
[2025-05-26T09:07:32.758Z] [INFO] Found 0 files to baseline
[2025-05-26T09:07:32.759Z] [INFO] Initial workspace scan completed: 0 files baselined
[2025-05-26T09:07:32.776Z] [INFO] Workspace marked as initialized
[2025-05-26T09:07:32.777Z] [INFO] Workspace initialization completed
[2025-05-26T09:07:32.777Z] [INFO] Starting change processor
[2025-05-26T09:07:32.777Z] [INFO] Starting to listen for file changes
[2025-05-26T09:07:32.779Z] [INFO] Learning Docs extension activated
[2025-05-26T09:08:59.442Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:08:59.444Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.447Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:08:59.448Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:08:59.488Z] [INFO] File created: README.md
[2025-05-26T09:08:59.489Z] [INFO] Processing change: create - README.md
[2025-05-26T09:08:59.491Z] [INFO] Generated diff for: README.md
[2025-05-26T09:08:59.491Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for README.md
[2025-05-26T09:08:59.491Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:08:59.492Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:08:59.492Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:08:59.492Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.493Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:08:59.493Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:08:59.494Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:08:59.494Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:08:59.494Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:08:59.495Z] [INFO]   - Event type: create
[2025-05-26T09:08:59.495Z] [INFO]   - Has current content: true
[2025-05-26T09:08:59.495Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:08:59.496Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:08:59.497Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.499Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:08:59.499Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:08:59.500Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:08:59.501Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for README.md
[2025-05-26T09:08:59.503Z] [INFO] 📝 Detected language: markdown
[2025-05-26T09:08:59.504Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/README.md
[2025-05-26T09:08:59.507Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\README.md
[2025-05-26T09:08:59.507Z] [INFO] 📝 Language: markdown
[2025-05-26T09:08:59.508Z] [INFO] 📄 Current content length: 3194
[2025-05-26T09:08:59.508Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:08:59.509Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:08:59.509Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:08:59.510Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.511Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:08:59.511Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:08:59.512Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:08:59.512Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:08:59.513Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:08:59.513Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.514Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:08:59.515Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:08:59.516Z] [INFO] 🌐 Language markdown supported: false
[2025-05-26T09:08:59.516Z] [WARN] ❌ Semantic analysis not enabled for language: markdown
[2025-05-26T09:08:59.575Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for README.md
[2025-05-26T09:08:59.576Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:08:59.576Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:08:59.576Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.577Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:08:59.577Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:08:59.577Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:08:59.578Z] [INFO] Getting contextual info for: README.md
[2025-05-26T09:08:59.689Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- README.md
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:08:59.918Z] [INFO] Contextual analysis completed for: README.md
[2025-05-26T09:08:59.918Z] [INFO] Contextual analysis completed for README.md
[2025-05-26T09:08:59.918Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:08:59.919Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:08:59.919Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.920Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:08:59.920Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:08:59.921Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:08:59.921Z] [INFO] Generating AI documentation for: README.md
[2025-05-26T09:08:59.921Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:08:59.922Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:08:59.922Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.922Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:08:59.923Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:08:59.923Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:08:59.923Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:08:59.924Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:08:59.924Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:08:59.924Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:08:59.928Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:08:59.929Z] [WARN] Failed to generate AI documentation for: README.md
[2025-05-26T09:08:59.929Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 7c1e9dfee3c94495f1d5678f309632fd
[2025-05-26T09:08:59.930Z] [INFO] 📄 File: README.md
[2025-05-26T09:08:59.930Z] [INFO] 🔄 Event type: create
[2025-05-26T09:08:59.930Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:08:59.937Z] [INFO] ✅ SAVED documentation record: 7c1e9dfee3c94495f1d5678f309632fd to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\7c1e9dfee3c94495f1d5678f309632fd.json
[2025-05-26T09:08:59.942Z] [INFO] Successfully processed change: 7c1e9dfee3c94495f1d5678f309632fd
[2025-05-26T09:09:05.963Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:09:05.964Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.965Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:09:05.965Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:09:05.973Z] [INFO] File created: requirements.txt
[2025-05-26T09:09:05.974Z] [INFO] Processing change: create - requirements.txt
[2025-05-26T09:09:05.974Z] [INFO] Generated diff for: requirements.txt
[2025-05-26T09:09:05.974Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for requirements.txt
[2025-05-26T09:09:05.975Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:09:05.975Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:05.975Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:05.975Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.976Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:05.976Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:05.976Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:05.977Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:09:05.977Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:09:05.977Z] [INFO]   - Event type: create
[2025-05-26T09:09:05.977Z] [INFO]   - Has current content: true
[2025-05-26T09:09:05.978Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:05.978Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:05.978Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.979Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:05.979Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:05.979Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:05.979Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for requirements.txt
[2025-05-26T09:09:05.980Z] [INFO] 📝 Detected language: plaintext
[2025-05-26T09:09:05.980Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/requirements.txt
[2025-05-26T09:09:05.980Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\requirements.txt
[2025-05-26T09:09:05.981Z] [INFO] 📝 Language: plaintext
[2025-05-26T09:09:05.981Z] [INFO] 📄 Current content length: 43
[2025-05-26T09:09:05.981Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:09:05.981Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:05.982Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:05.982Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.983Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:05.983Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:05.983Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:05.983Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:09:05.984Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:09:05.984Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.985Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:09:05.985Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:09:05.986Z] [INFO] 🌐 Language plaintext supported: false
[2025-05-26T09:09:05.987Z] [WARN] ❌ Semantic analysis not enabled for language: plaintext
[2025-05-26T09:09:05.988Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for requirements.txt
[2025-05-26T09:09:05.988Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:09:05.989Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:09:05.989Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:05.989Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:05.990Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:09:05.990Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:09:05.990Z] [INFO] Getting contextual info for: requirements.txt
[2025-05-26T09:09:06.175Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- requirements.txt
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:09:06.959Z] [INFO] Contextual analysis completed for: requirements.txt
[2025-05-26T09:09:06.959Z] [INFO] Contextual analysis completed for requirements.txt
[2025-05-26T09:09:06.960Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:06.960Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:06.960Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:06.961Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:06.961Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:06.961Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:06.962Z] [INFO] Generating AI documentation for: requirements.txt
[2025-05-26T09:09:06.962Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:06.962Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:06.962Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:06.963Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:06.963Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:06.964Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:06.964Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:09:06.964Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:06.965Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:09:06.965Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:09:06.972Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:09:06.973Z] [WARN] Failed to generate AI documentation for: requirements.txt
[2025-05-26T09:09:06.973Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: de5ae50ff14f1ddefc469785373d58ed
[2025-05-26T09:09:06.973Z] [INFO] 📄 File: requirements.txt
[2025-05-26T09:09:06.974Z] [INFO] 🔄 Event type: create
[2025-05-26T09:09:06.974Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:09:06.977Z] [INFO] ✅ SAVED documentation record: de5ae50ff14f1ddefc469785373d58ed to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\de5ae50ff14f1ddefc469785373d58ed.json
[2025-05-26T09:09:06.980Z] [INFO] Successfully processed change: de5ae50ff14f1ddefc469785373d58ed
[2025-05-26T09:09:22.516Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:09:22.517Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:22.519Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:09:22.520Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:09:23.243Z] [INFO] File created: app.py
[2025-05-26T09:09:23.243Z] [INFO] Processing change: create - app.py
[2025-05-26T09:09:23.244Z] [INFO] Generated diff for: app.py
[2025-05-26T09:09:23.244Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for app.py
[2025-05-26T09:09:23.245Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:09:23.245Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:23.245Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:23.246Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.247Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:23.247Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:23.247Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:23.248Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:09:23.248Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:09:23.248Z] [INFO]   - Event type: create
[2025-05-26T09:09:23.249Z] [INFO]   - Has current content: true
[2025-05-26T09:09:23.249Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:23.249Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:23.250Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.250Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:23.251Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:23.251Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:23.251Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for app.py
[2025-05-26T09:09:23.252Z] [INFO] 📝 Detected language: python
[2025-05-26T09:09:23.253Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/app.py
[2025-05-26T09:09:23.253Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\app.py
[2025-05-26T09:09:23.254Z] [INFO] 📝 Language: python
[2025-05-26T09:09:23.254Z] [INFO] 📄 Current content length: 3303
[2025-05-26T09:09:23.255Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:09:23.255Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:23.255Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:23.256Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.256Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:23.257Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:23.257Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:23.258Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:09:23.258Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:09:23.258Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.259Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:09:23.260Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:09:23.260Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:09:23.261Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\app.py
[2025-05-26T09:09:23.261Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:09:23.262Z] [INFO] 🔍 Getting document symbols for python (3303 chars)
[2025-05-26T09:09:23.263Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/app.py
[2025-05-26T09:09:23.263Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:09:23.465Z] [INFO] 📊 LSP returned 11 symbols from open document
[2025-05-26T09:09:23.466Z] [INFO] ✅ Found 11 current symbols
[2025-05-26T09:09:23.466Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:09:23.466Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:09:23.467Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:09:23.467Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 11
[2025-05-26T09:09:23.468Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:09:23.468Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:09:23.468Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.469Z] [INFO]       Range: 8:0-8:3
[2025-05-26T09:09:23.469Z] [INFO]       Selection: 8:0-8:3
[2025-05-26T09:09:23.470Z] [INFO]   2. users (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.471Z] [INFO]       Range: 12:0-12:5
[2025-05-26T09:09:23.471Z] [INFO]       Selection: 12:0-12:5
[2025-05-26T09:09:23.472Z] [INFO]   3. posts (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.472Z] [INFO]       Range: 13:0-13:5
[2025-05-26T09:09:23.472Z] [INFO]       Selection: 13:0-13:5
[2025-05-26T09:09:23.473Z] [INFO]   4. home (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.473Z] [INFO]       Range: 19:0-22:53
[2025-05-26T09:09:23.473Z] [INFO]       Selection: 20:4-20:8
[2025-05-26T09:09:23.474Z] [INFO]   5. about (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.474Z] [INFO]       Range: 25:0-28:40
[2025-05-26T09:09:23.475Z] [INFO]       Selection: 26:4-26:9
[2025-05-26T09:09:23.475Z] [INFO]   6. user_profile (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.475Z] [INFO]       Range: 31:0-34:58
[2025-05-26T09:09:23.476Z] [INFO]       Selection: 32:4-32:16
[2025-05-26T09:09:23.476Z] [INFO]     1. username (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.476Z] [INFO]         Range: 32:17-32:25
[2025-05-26T09:09:23.477Z] [INFO]   7. contact (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.477Z] [INFO]       Range: 37:0-54:42
[2025-05-26T09:09:23.478Z] [INFO]       Selection: 38:4-38:11
[2025-05-26T09:09:23.478Z] [INFO]     1. name (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.479Z] [INFO]         Range: 42:8-42:12
[2025-05-26T09:09:23.479Z] [INFO]     2. email (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.480Z] [INFO]         Range: 43:8-43:13
[2025-05-26T09:09:23.480Z] [INFO]     3. message (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.481Z] [INFO]         Range: 44:8-44:15
[2025-05-26T09:09:23.481Z] [INFO]   8. api_posts (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.481Z] [INFO]       Range: 57:0-60:27
[2025-05-26T09:09:23.482Z] [INFO]       Selection: 58:4-58:13
[2025-05-26T09:09:23.482Z] [INFO]   9. add_post (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.482Z] [INFO]       Range: 63:0-82:43
[2025-05-26T09:09:23.483Z] [INFO]       Selection: 64:4-64:12
[2025-05-26T09:09:23.483Z] [INFO]     1. title (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.483Z] [INFO]         Range: 67:8-67:13
[2025-05-26T09:09:23.484Z] [INFO]     2. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.484Z] [INFO]         Range: 68:8-68:15
[2025-05-26T09:09:23.484Z] [INFO]     3. new_post (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.485Z] [INFO]         Range: 71:12-71:20
[2025-05-26T09:09:23.485Z] [INFO]   10. page_not_found (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.486Z] [INFO]       Range: 85:0-88:43
[2025-05-26T09:09:23.486Z] [INFO]       Selection: 86:4-86:18
[2025-05-26T09:09:23.486Z] [INFO]     1. error (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.487Z] [INFO]         Range: 86:19-86:24
[2025-05-26T09:09:23.487Z] [INFO]   11. truncate_filter (Kind: Function, Detail: 'none')
[2025-05-26T09:09:23.488Z] [INFO]       Range: 91:0-96:32
[2025-05-26T09:09:23.488Z] [INFO]       Selection: 92:4-92:19
[2025-05-26T09:09:23.489Z] [INFO]     1. text (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.489Z] [INFO]         Range: 92:20-92:24
[2025-05-26T09:09:23.489Z] [INFO]     2. length (Kind: Variable, Detail: 'none')
[2025-05-26T09:09:23.490Z] [INFO]         Range: 92:26-92:35
[2025-05-26T09:09:23.490Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:09:23.491Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.491Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:09:23.492Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:09:23.492Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:09:23.493Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:09:23.493Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 21 symbols
[2025-05-26T09:09:23.493Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:09:23.494Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (21 entries):
[2025-05-26T09:09:23.494Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:09:23.495Z] [INFO]   "users:12" -> users (Variable, Detail: 'none')
[2025-05-26T09:09:23.495Z] [INFO]   "posts:12" -> posts (Variable, Detail: 'none')
[2025-05-26T09:09:23.495Z] [INFO]   "home:11" -> home (Function, Detail: 'none')
[2025-05-26T09:09:23.496Z] [INFO]   "about:11" -> about (Function, Detail: 'none')
[2025-05-26T09:09:23.496Z] [INFO]   "user_profile:11" -> user_profile (Function, Detail: 'none')
[2025-05-26T09:09:23.496Z] [INFO]   "user_profile.username:12" -> username (Variable, Detail: 'none')
[2025-05-26T09:09:23.497Z] [INFO]   "contact:11" -> contact (Function, Detail: 'none')
[2025-05-26T09:09:23.497Z] [INFO]   "contact.name:12" -> name (Variable, Detail: 'none')
[2025-05-26T09:09:23.497Z] [INFO]   "contact.email:12" -> email (Variable, Detail: 'none')
[2025-05-26T09:09:23.498Z] [INFO]   "contact.message:12" -> message (Variable, Detail: 'none')
[2025-05-26T09:09:23.498Z] [INFO]   "api_posts:11" -> api_posts (Function, Detail: 'none')
[2025-05-26T09:09:23.498Z] [INFO]   "add_post:11" -> add_post (Function, Detail: 'none')
[2025-05-26T09:09:23.499Z] [INFO]   "add_post.title:12" -> title (Variable, Detail: 'none')
[2025-05-26T09:09:23.499Z] [INFO]   "add_post.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:09:23.499Z] [INFO]   "add_post.new_post:12" -> new_post (Variable, Detail: 'none')
[2025-05-26T09:09:23.500Z] [INFO]   "page_not_found:11" -> page_not_found (Function, Detail: 'none')
[2025-05-26T09:09:23.500Z] [INFO]   "page_not_found.error:12" -> error (Variable, Detail: 'none')
[2025-05-26T09:09:23.500Z] [INFO]   "truncate_filter:11" -> truncate_filter (Function, Detail: 'none')
[2025-05-26T09:09:23.501Z] [INFO]   "truncate_filter.text:12" -> text (Variable, Detail: 'none')
[2025-05-26T09:09:23.501Z] [INFO]   "truncate_filter.length:12" -> length (Variable, Detail: 'none')
[2025-05-26T09:09:23.501Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:09:23.502Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:09:23.502Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users:12' -> users (Kind: 12)
[2025-05-26T09:09:23.503Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'posts:12' -> posts (Kind: 12)
[2025-05-26T09:09:23.503Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T09:09:23.504Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T09:09:23.504Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_profile:11' -> user_profile (Kind: 11)
[2025-05-26T09:09:23.505Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_profile.username:12' -> username (Kind: 12)
[2025-05-26T09:09:23.505Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact:11' -> contact (Kind: 11)
[2025-05-26T09:09:23.506Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.name:12' -> name (Kind: 12)
[2025-05-26T09:09:23.506Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.email:12' -> email (Kind: 12)
[2025-05-26T09:09:23.506Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'contact.message:12' -> message (Kind: 12)
[2025-05-26T09:09:23.507Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'api_posts:11' -> api_posts (Kind: 11)
[2025-05-26T09:09:23.507Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_post:11' -> add_post (Kind: 11)
[2025-05-26T09:09:23.508Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_post.title:12' -> title (Kind: 12)
[2025-05-26T09:09:23.508Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_post.content:12' -> content (Kind: 12)
[2025-05-26T09:09:23.508Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'add_post.new_post:12' -> new_post (Kind: 12)
[2025-05-26T09:09:23.509Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found:11' -> page_not_found (Kind: 11)
[2025-05-26T09:09:23.509Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found.error:12' -> error (Kind: 12)
[2025-05-26T09:09:23.509Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'truncate_filter:11' -> truncate_filter (Kind: 11)
[2025-05-26T09:09:23.510Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'truncate_filter.text:12' -> text (Kind: 12)
[2025-05-26T09:09:23.510Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'truncate_filter.length:12' -> length (Kind: 12)
[2025-05-26T09:09:23.510Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:09:23.511Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:09:23.511Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:09:23.511Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.512Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:09:23.513Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:09:23.513Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:09:23.513Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 21 primitives:
[2025-05-26T09:09:23.514Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:09:23.514Z] [INFO]   2. ADD variable: 'users'
[2025-05-26T09:09:23.515Z] [INFO]   3. ADD variable: 'posts'
[2025-05-26T09:09:23.516Z] [INFO]   4. ADD function: 'home'
[2025-05-26T09:09:23.516Z] [INFO]   5. ADD function: 'about'
[2025-05-26T09:09:23.517Z] [INFO]   6. ADD function: 'user_profile'
[2025-05-26T09:09:23.518Z] [INFO]   7. ADD variable: 'username'
[2025-05-26T09:09:23.518Z] [INFO]   8. ADD function: 'contact'
[2025-05-26T09:09:23.520Z] [INFO]   9. ADD variable: 'name'
[2025-05-26T09:09:23.521Z] [INFO]   10. ADD variable: 'email'
[2025-05-26T09:09:23.522Z] [INFO]   11. ADD variable: 'message'
[2025-05-26T09:09:23.523Z] [INFO]   12. ADD function: 'api_posts'
[2025-05-26T09:09:23.525Z] [INFO]   13. ADD function: 'add_post'
[2025-05-26T09:09:23.526Z] [INFO]   14. ADD variable: 'title'
[2025-05-26T09:09:23.532Z] [INFO]   15. ADD variable: 'content'
[2025-05-26T09:09:23.532Z] [INFO]   16. ADD variable: 'new_post'
[2025-05-26T09:09:23.533Z] [INFO]   17. ADD function: 'page_not_found'
[2025-05-26T09:09:23.533Z] [INFO]   18. ADD variable: 'error'
[2025-05-26T09:09:23.534Z] [INFO]   19. ADD function: 'truncate_filter'
[2025-05-26T09:09:23.534Z] [INFO]   20. ADD variable: 'text'
[2025-05-26T09:09:23.534Z] [INFO]   21. ADD variable: 'length'
[2025-05-26T09:09:23.545Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:09:23.546Z] [INFO] 🎯 Generated 21 semantic primitives
[2025-05-26T09:09:23.546Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:09:23.547Z] [INFO]   2. ADD variable: 'users'
[2025-05-26T09:09:23.547Z] [INFO]   3. ADD variable: 'posts'
[2025-05-26T09:09:23.548Z] [INFO]   4. ADD function: 'home'
[2025-05-26T09:09:23.548Z] [INFO]   5. ADD function: 'about'
[2025-05-26T09:09:23.549Z] [INFO]   6. ADD function: 'user_profile'
[2025-05-26T09:09:23.550Z] [INFO]   7. ADD variable: 'username'
[2025-05-26T09:09:23.551Z] [INFO]   8. ADD function: 'contact'
[2025-05-26T09:09:23.551Z] [INFO]   9. ADD variable: 'name'
[2025-05-26T09:09:23.553Z] [INFO]   10. ADD variable: 'email'
[2025-05-26T09:09:23.555Z] [INFO]   11. ADD variable: 'message'
[2025-05-26T09:09:23.556Z] [INFO]   12. ADD function: 'api_posts'
[2025-05-26T09:09:23.556Z] [INFO]   13. ADD function: 'add_post'
[2025-05-26T09:09:23.557Z] [INFO]   14. ADD variable: 'title'
[2025-05-26T09:09:23.557Z] [INFO]   15. ADD variable: 'content'
[2025-05-26T09:09:23.558Z] [INFO]   16. ADD variable: 'new_post'
[2025-05-26T09:09:23.558Z] [INFO]   17. ADD function: 'page_not_found'
[2025-05-26T09:09:23.559Z] [INFO]   18. ADD variable: 'error'
[2025-05-26T09:09:23.559Z] [INFO]   19. ADD function: 'truncate_filter'
[2025-05-26T09:09:23.560Z] [INFO]   20. ADD variable: 'text'
[2025-05-26T09:09:23.560Z] [INFO]   21. ADD variable: 'length'
[2025-05-26T09:09:23.561Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:09:23.561Z] [INFO] 📊 Summary: +21 -0 ~0 ↔0
[2025-05-26T09:09:23.562Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 21 primitives found
[2025-05-26T09:09:23.566Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for app.py: 21 primitives found
[2025-05-26T09:09:23.567Z] [INFO] 📊 Primitives stored in record: 21
[2025-05-26T09:09:23.567Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:09:23.568Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:09:23.572Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:23.577Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:23.578Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:09:23.578Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:09:23.578Z] [INFO] Getting contextual info for: app.py
[2025-05-26T09:09:24.799Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- app.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:09:25.004Z] [INFO] Contextual analysis completed for: app.py
[2025-05-26T09:09:25.005Z] [INFO] Contextual analysis completed for app.py
[2025-05-26T09:09:25.005Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:25.005Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:25.006Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:25.006Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:25.006Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:25.007Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:25.007Z] [INFO] Generating AI documentation for: app.py
[2025-05-26T09:09:25.007Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:25.008Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:25.008Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:25.009Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:25.009Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:25.009Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:25.010Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:09:25.010Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:25.011Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:09:25.011Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:09:25.017Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:09:25.018Z] [WARN] Failed to generate AI documentation for: app.py
[2025-05-26T09:09:25.019Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: cf814fa1031794aa6320ffc68acad235
[2025-05-26T09:09:25.019Z] [INFO] 📄 File: app.py
[2025-05-26T09:09:25.020Z] [INFO] 🔄 Event type: create
[2025-05-26T09:09:25.021Z] [INFO] 🔧 Semantic primitives: 21
[2025-05-26T09:09:25.021Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:09:25.022Z] [INFO]   2. add variable: 'users'
[2025-05-26T09:09:25.022Z] [INFO]   3. add variable: 'posts'
[2025-05-26T09:09:25.022Z] [INFO]   4. add function: 'home'
[2025-05-26T09:09:25.023Z] [INFO]   5. add function: 'about'
[2025-05-26T09:09:25.023Z] [INFO]   6. add function: 'user_profile'
[2025-05-26T09:09:25.023Z] [INFO]   7. add variable: 'username'
[2025-05-26T09:09:25.024Z] [INFO]   8. add function: 'contact'
[2025-05-26T09:09:25.024Z] [INFO]   9. add variable: 'name'
[2025-05-26T09:09:25.024Z] [INFO]   10. add variable: 'email'
[2025-05-26T09:09:25.025Z] [INFO]   11. add variable: 'message'
[2025-05-26T09:09:25.025Z] [INFO]   12. add function: 'api_posts'
[2025-05-26T09:09:25.025Z] [INFO]   13. add function: 'add_post'
[2025-05-26T09:09:25.026Z] [INFO]   14. add variable: 'title'
[2025-05-26T09:09:25.026Z] [INFO]   15. add variable: 'content'
[2025-05-26T09:09:25.026Z] [INFO]   16. add variable: 'new_post'
[2025-05-26T09:09:25.027Z] [INFO]   17. add function: 'page_not_found'
[2025-05-26T09:09:25.027Z] [INFO]   18. add variable: 'error'
[2025-05-26T09:09:25.027Z] [INFO]   19. add function: 'truncate_filter'
[2025-05-26T09:09:25.028Z] [INFO]   20. add variable: 'text'
[2025-05-26T09:09:25.028Z] [INFO]   21. add variable: 'length'
[2025-05-26T09:09:25.047Z] [INFO] ✅ SAVED documentation record: cf814fa1031794aa6320ffc68acad235 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\cf814fa1031794aa6320ffc68acad235.json
[2025-05-26T09:09:25.062Z] [INFO] Successfully processed change: cf814fa1031794aa6320ffc68acad235
[2025-05-26T09:09:40.267Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:09:40.268Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.269Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:09:40.270Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:09:40.271Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:09:40.272Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.273Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:09:40.273Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:09:40.294Z] [INFO] Directory created: templates (skipping content analysis)
[2025-05-26T09:09:40.294Z] [INFO] File created: templates/base.html
[2025-05-26T09:09:40.295Z] [INFO] Processing change: create - templates/base.html
[2025-05-26T09:09:40.295Z] [INFO] Generated diff for: templates/base.html
[2025-05-26T09:09:40.296Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/base.html
[2025-05-26T09:09:40.296Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:09:40.296Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:40.296Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:40.297Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.297Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:40.298Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:40.298Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:40.298Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:09:40.299Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:09:40.299Z] [INFO]   - Event type: create
[2025-05-26T09:09:40.299Z] [INFO]   - Has current content: true
[2025-05-26T09:09:40.300Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:40.300Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:40.300Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.301Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:40.301Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:40.301Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:40.302Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/base.html
[2025-05-26T09:09:40.302Z] [INFO] 📝 Detected language: html
[2025-05-26T09:09:40.303Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/base.html
[2025-05-26T09:09:40.303Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\base.html
[2025-05-26T09:09:40.303Z] [INFO] 📝 Language: html
[2025-05-26T09:09:40.304Z] [INFO] 📄 Current content length: 3284
[2025-05-26T09:09:40.304Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:09:40.304Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:40.305Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:40.305Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.306Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:40.306Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:40.306Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:40.307Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:09:40.307Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:09:40.307Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.308Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:09:40.308Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:09:40.309Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:09:40.309Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:09:40.310Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/base.html
[2025-05-26T09:09:40.310Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:09:40.310Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:09:40.311Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.311Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:40.312Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:09:40.312Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:09:40.312Z] [INFO] Getting contextual info for: templates/base.html
[2025-05-26T09:09:40.408Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/base.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:09:40.611Z] [INFO] Contextual analysis completed for: templates/base.html
[2025-05-26T09:09:40.612Z] [INFO] Contextual analysis completed for templates/base.html
[2025-05-26T09:09:40.612Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:40.612Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:40.613Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.613Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:40.613Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:40.614Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:40.614Z] [INFO] Generating AI documentation for: templates/base.html
[2025-05-26T09:09:40.614Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:40.615Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:40.615Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.615Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:40.616Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:40.616Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:40.616Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:09:40.617Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:40.617Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:09:40.617Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:09:40.621Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:09:40.622Z] [WARN] Failed to generate AI documentation for: templates/base.html
[2025-05-26T09:09:40.622Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 59e9f720237c24d06b4089dfa07bf9f1
[2025-05-26T09:09:40.622Z] [INFO] 📄 File: templates/base.html
[2025-05-26T09:09:40.623Z] [INFO] 🔄 Event type: create
[2025-05-26T09:09:40.623Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:09:40.632Z] [INFO] ✅ SAVED documentation record: 59e9f720237c24d06b4089dfa07bf9f1 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\59e9f720237c24d06b4089dfa07bf9f1.json
[2025-05-26T09:09:40.639Z] [INFO] Successfully processed change: 59e9f720237c24d06b4089dfa07bf9f1
[2025-05-26T09:09:54.314Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:09:54.315Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.316Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:09:54.316Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:09:54.320Z] [INFO] File created: templates/index.html
[2025-05-26T09:09:54.321Z] [INFO] Processing change: create - templates/index.html
[2025-05-26T09:09:54.321Z] [INFO] Generated diff for: templates/index.html
[2025-05-26T09:09:54.322Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/index.html
[2025-05-26T09:09:54.322Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:09:54.322Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:54.322Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:54.323Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.323Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:54.324Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:54.324Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:54.324Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:09:54.325Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:09:54.325Z] [INFO]   - Event type: create
[2025-05-26T09:09:54.325Z] [INFO]   - Has current content: true
[2025-05-26T09:09:54.325Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:54.326Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:54.326Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.327Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:54.327Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:54.328Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:54.328Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/index.html
[2025-05-26T09:09:54.328Z] [INFO] 📝 Detected language: html
[2025-05-26T09:09:54.329Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/index.html
[2025-05-26T09:09:54.329Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\index.html
[2025-05-26T09:09:54.329Z] [INFO] 📝 Language: html
[2025-05-26T09:09:54.329Z] [INFO] 📄 Current content length: 1834
[2025-05-26T09:09:54.330Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:09:54.330Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:09:54.330Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:09:54.331Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.331Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:54.332Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:09:54.332Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:09:54.332Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:09:54.332Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:09:54.333Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.333Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:09:54.334Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:09:54.334Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:09:54.334Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:09:54.335Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/index.html
[2025-05-26T09:09:54.336Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:09:54.336Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:09:54.336Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.337Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:09:54.338Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:09:54.338Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:09:54.339Z] [INFO] Getting contextual info for: templates/index.html
[2025-05-26T09:09:54.426Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/index.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:09:54.638Z] [INFO] Contextual analysis completed for: templates/index.html
[2025-05-26T09:09:54.639Z] [INFO] Contextual analysis completed for templates/index.html
[2025-05-26T09:09:54.639Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:54.639Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:54.640Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.640Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:54.640Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:54.641Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:54.641Z] [INFO] Generating AI documentation for: templates/index.html
[2025-05-26T09:09:54.641Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:09:54.641Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:09:54.642Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.642Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:09:54.643Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:09:54.643Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:09:54.643Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:09:54.643Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:09:54.644Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:09:54.644Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:09:54.649Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:09:54.650Z] [WARN] Failed to generate AI documentation for: templates/index.html
[2025-05-26T09:09:54.650Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 3bfc011eb399a69ff5886a6de50ff6d0
[2025-05-26T09:09:54.650Z] [INFO] 📄 File: templates/index.html
[2025-05-26T09:09:54.651Z] [INFO] 🔄 Event type: create
[2025-05-26T09:09:54.651Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:09:54.659Z] [INFO] ✅ SAVED documentation record: 3bfc011eb399a69ff5886a6de50ff6d0 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\3bfc011eb399a69ff5886a6de50ff6d0.json
[2025-05-26T09:09:54.667Z] [INFO] Successfully processed change: 3bfc011eb399a69ff5886a6de50ff6d0
[2025-05-26T09:10:15.562Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:10:15.563Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.564Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:10:15.564Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:10:15.567Z] [INFO] File created: templates/about.html
[2025-05-26T09:10:15.568Z] [INFO] Processing change: create - templates/about.html
[2025-05-26T09:10:15.568Z] [INFO] Generated diff for: templates/about.html
[2025-05-26T09:10:15.569Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/about.html
[2025-05-26T09:10:15.569Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:10:15.569Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:15.570Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:15.570Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.570Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:15.571Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:15.571Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:15.572Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:10:15.572Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:10:15.572Z] [INFO]   - Event type: create
[2025-05-26T09:10:15.573Z] [INFO]   - Has current content: true
[2025-05-26T09:10:15.573Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:15.573Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:15.574Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.574Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:15.574Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:15.575Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:15.575Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/about.html
[2025-05-26T09:10:15.575Z] [INFO] 📝 Detected language: html
[2025-05-26T09:10:15.575Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/about.html
[2025-05-26T09:10:15.576Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\about.html
[2025-05-26T09:10:15.576Z] [INFO] 📝 Language: html
[2025-05-26T09:10:15.576Z] [INFO] 📄 Current content length: 3896
[2025-05-26T09:10:15.577Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:10:15.577Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:15.577Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:15.577Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.578Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:15.578Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:15.578Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:15.579Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:10:15.580Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:10:15.580Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.580Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:10:15.581Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:10:15.581Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:10:15.581Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:10:15.582Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/about.html
[2025-05-26T09:10:15.582Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:10:15.583Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:10:15.583Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.584Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:15.584Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:10:15.584Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:10:15.584Z] [INFO] Getting contextual info for: templates/about.html
[2025-05-26T09:10:15.672Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/about.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:10:15.871Z] [INFO] Contextual analysis completed for: templates/about.html
[2025-05-26T09:10:15.872Z] [INFO] Contextual analysis completed for templates/about.html
[2025-05-26T09:10:15.872Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:10:15.872Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:10:15.873Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.873Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:10:15.874Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:10:15.874Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:10:15.874Z] [INFO] Generating AI documentation for: templates/about.html
[2025-05-26T09:10:15.874Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:10:15.875Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:10:15.875Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.875Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:10:15.876Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:10:15.876Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:10:15.876Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:10:15.877Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:15.877Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:10:15.878Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:10:15.881Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:10:15.881Z] [WARN] Failed to generate AI documentation for: templates/about.html
[2025-05-26T09:10:15.881Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: eb813e600425baa89d700e969a2c6363
[2025-05-26T09:10:15.882Z] [INFO] 📄 File: templates/about.html
[2025-05-26T09:10:15.882Z] [INFO] 🔄 Event type: create
[2025-05-26T09:10:15.882Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:10:15.891Z] [INFO] ✅ SAVED documentation record: eb813e600425baa89d700e969a2c6363 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\eb813e600425baa89d700e969a2c6363.json
[2025-05-26T09:10:15.899Z] [INFO] Successfully processed change: eb813e600425baa89d700e969a2c6363
[2025-05-26T09:10:38.066Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:10:38.066Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.067Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:10:38.068Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:10:38.072Z] [INFO] File created: templates/contact.html
[2025-05-26T09:10:38.072Z] [INFO] Processing change: create - templates/contact.html
[2025-05-26T09:10:38.073Z] [INFO] Generated diff for: templates/contact.html
[2025-05-26T09:10:38.073Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/contact.html
[2025-05-26T09:10:38.073Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:10:38.074Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:38.074Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:38.074Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.075Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:38.075Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:38.075Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:38.075Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:10:38.076Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:10:38.076Z] [INFO]   - Event type: create
[2025-05-26T09:10:38.076Z] [INFO]   - Has current content: true
[2025-05-26T09:10:38.076Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:38.077Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:38.077Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.077Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:38.078Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:38.078Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:38.078Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/contact.html
[2025-05-26T09:10:38.078Z] [INFO] 📝 Detected language: html
[2025-05-26T09:10:38.079Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/contact.html
[2025-05-26T09:10:38.079Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\contact.html
[2025-05-26T09:10:38.079Z] [INFO] 📝 Language: html
[2025-05-26T09:10:38.079Z] [INFO] 📄 Current content length: 3861
[2025-05-26T09:10:38.079Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:10:38.080Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:10:38.080Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:10:38.080Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.081Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:38.082Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:10:38.082Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:10:38.082Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:10:38.083Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:10:38.083Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.083Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:10:38.084Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:10:38.084Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:10:38.084Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:10:38.085Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/contact.html
[2025-05-26T09:10:38.085Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:10:38.086Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:10:38.086Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.087Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:10:38.087Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:10:38.088Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:10:38.088Z] [INFO] Getting contextual info for: templates/contact.html
[2025-05-26T09:10:38.190Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/contact.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:10:38.394Z] [INFO] Contextual analysis completed for: templates/contact.html
[2025-05-26T09:10:38.395Z] [INFO] Contextual analysis completed for templates/contact.html
[2025-05-26T09:10:38.395Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:10:38.395Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:10:38.396Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.396Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:10:38.397Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:10:38.397Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:10:38.397Z] [INFO] Generating AI documentation for: templates/contact.html
[2025-05-26T09:10:38.397Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:10:38.398Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:10:38.398Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.399Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:10:38.399Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:10:38.399Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:10:38.399Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:10:38.400Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:10:38.400Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:10:38.400Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:10:38.406Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:10:38.406Z] [WARN] Failed to generate AI documentation for: templates/contact.html
[2025-05-26T09:10:38.407Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 0f827e5b266b0efb269c1a5468b0d934
[2025-05-26T09:10:38.407Z] [INFO] 📄 File: templates/contact.html
[2025-05-26T09:10:38.407Z] [INFO] 🔄 Event type: create
[2025-05-26T09:10:38.407Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:10:38.431Z] [INFO] ✅ SAVED documentation record: 0f827e5b266b0efb269c1a5468b0d934 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\0f827e5b266b0efb269c1a5468b0d934.json
[2025-05-26T09:10:38.439Z] [INFO] Successfully processed change: 0f827e5b266b0efb269c1a5468b0d934
[2025-05-26T09:11:03.610Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:11:03.611Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.612Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:11:03.612Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:11:03.616Z] [INFO] File created: templates/user.html
[2025-05-26T09:11:03.616Z] [INFO] Processing change: create - templates/user.html
[2025-05-26T09:11:03.617Z] [INFO] Generated diff for: templates/user.html
[2025-05-26T09:11:03.617Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/user.html
[2025-05-26T09:11:03.617Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:11:03.618Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:03.618Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:03.618Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.620Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:03.620Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:03.620Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:03.621Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:11:03.621Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:11:03.621Z] [INFO]   - Event type: create
[2025-05-26T09:11:03.622Z] [INFO]   - Has current content: true
[2025-05-26T09:11:03.622Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:03.622Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:03.623Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.623Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:03.623Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:03.624Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:03.624Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/user.html
[2025-05-26T09:11:03.624Z] [INFO] 📝 Detected language: html
[2025-05-26T09:11:03.625Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/user.html
[2025-05-26T09:11:03.625Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\user.html
[2025-05-26T09:11:03.625Z] [INFO] 📝 Language: html
[2025-05-26T09:11:03.625Z] [INFO] 📄 Current content length: 4129
[2025-05-26T09:11:03.625Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:11:03.626Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:03.626Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:03.626Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.627Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:03.627Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:03.628Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:03.628Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:11:03.628Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:11:03.629Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.629Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:11:03.630Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:11:03.630Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:11:03.631Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:11:03.632Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/user.html
[2025-05-26T09:11:03.632Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:11:03.633Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:11:03.633Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.634Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:03.634Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:11:03.634Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:11:03.635Z] [INFO] Getting contextual info for: templates/user.html
[2025-05-26T09:11:03.724Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/user.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:11:03.992Z] [INFO] Contextual analysis completed for: templates/user.html
[2025-05-26T09:11:03.993Z] [INFO] Contextual analysis completed for templates/user.html
[2025-05-26T09:11:03.993Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:03.993Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:03.993Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.994Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:03.994Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:03.995Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:03.995Z] [INFO] Generating AI documentation for: templates/user.html
[2025-05-26T09:11:03.995Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:03.995Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:03.996Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.996Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:03.996Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:03.997Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:03.997Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:11:03.997Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:03.998Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:11:03.998Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:11:04.002Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:11:04.002Z] [WARN] Failed to generate AI documentation for: templates/user.html
[2025-05-26T09:11:04.003Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: fe864f65e8d7abc5260cbe3f0fb99432
[2025-05-26T09:11:04.004Z] [INFO] 📄 File: templates/user.html
[2025-05-26T09:11:04.004Z] [INFO] 🔄 Event type: create
[2025-05-26T09:11:04.005Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:11:04.015Z] [INFO] ✅ SAVED documentation record: fe864f65e8d7abc5260cbe3f0fb99432 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\fe864f65e8d7abc5260cbe3f0fb99432.json
[2025-05-26T09:11:04.024Z] [INFO] Successfully processed change: fe864f65e8d7abc5260cbe3f0fb99432
[2025-05-26T09:11:28.501Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:11:28.502Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.503Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:11:28.504Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:11:28.509Z] [INFO] File created: templates/add_post.html
[2025-05-26T09:11:28.509Z] [INFO] Processing change: create - templates/add_post.html
[2025-05-26T09:11:28.510Z] [INFO] Generated diff for: templates/add_post.html
[2025-05-26T09:11:28.510Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/add_post.html
[2025-05-26T09:11:28.510Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:11:28.510Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:28.511Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:28.511Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.511Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:28.512Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:28.512Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:28.512Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:11:28.513Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:11:28.513Z] [INFO]   - Event type: create
[2025-05-26T09:11:28.513Z] [INFO]   - Has current content: true
[2025-05-26T09:11:28.513Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:28.514Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:28.514Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.515Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:28.515Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:28.515Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:28.515Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/add_post.html
[2025-05-26T09:11:28.516Z] [INFO] 📝 Detected language: html
[2025-05-26T09:11:28.516Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/add_post.html
[2025-05-26T09:11:28.517Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\add_post.html
[2025-05-26T09:11:28.517Z] [INFO] 📝 Language: html
[2025-05-26T09:11:28.517Z] [INFO] 📄 Current content length: 4566
[2025-05-26T09:11:28.519Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:11:28.519Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:28.519Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:28.520Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.521Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:28.521Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:28.522Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:28.523Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:11:28.523Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:11:28.524Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.524Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:11:28.525Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:11:28.526Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:11:28.526Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:11:28.527Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/add_post.html
[2025-05-26T09:11:28.528Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:11:28.529Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:11:28.529Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.531Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:28.532Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:11:28.532Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:11:28.533Z] [INFO] Getting contextual info for: templates/add_post.html
[2025-05-26T09:11:28.645Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/add_post.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:11:28.849Z] [INFO] Contextual analysis completed for: templates/add_post.html
[2025-05-26T09:11:28.850Z] [INFO] Contextual analysis completed for templates/add_post.html
[2025-05-26T09:11:28.850Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:28.850Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:28.851Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.851Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:28.851Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:28.852Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:28.852Z] [INFO] Generating AI documentation for: templates/add_post.html
[2025-05-26T09:11:28.852Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:28.853Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:28.853Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.854Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:28.855Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:28.855Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:28.856Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:11:28.856Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:28.857Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:11:28.857Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:11:28.861Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:11:28.862Z] [WARN] Failed to generate AI documentation for: templates/add_post.html
[2025-05-26T09:11:28.862Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 704dca00594eecd87a34796b67d9fedf
[2025-05-26T09:11:28.863Z] [INFO] 📄 File: templates/add_post.html
[2025-05-26T09:11:28.863Z] [INFO] 🔄 Event type: create
[2025-05-26T09:11:28.863Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:11:28.881Z] [INFO] ✅ SAVED documentation record: 704dca00594eecd87a34796b67d9fedf to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\704dca00594eecd87a34796b67d9fedf.json
[2025-05-26T09:11:28.890Z] [INFO] Successfully processed change: 704dca00594eecd87a34796b67d9fedf
[2025-05-26T09:11:50.586Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:11:50.590Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.593Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:11:50.595Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:11:50.603Z] [INFO] File created: templates/404.html
[2025-05-26T09:11:50.610Z] [INFO] Processing change: create - templates/404.html
[2025-05-26T09:11:50.610Z] [INFO] Generated diff for: templates/404.html
[2025-05-26T09:11:50.611Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for templates/404.html
[2025-05-26T09:11:50.611Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:11:50.611Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:50.612Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:50.612Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.613Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:50.613Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:50.613Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:50.614Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:11:50.614Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:11:50.614Z] [INFO]   - Event type: create
[2025-05-26T09:11:50.614Z] [INFO]   - Has current content: true
[2025-05-26T09:11:50.615Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:50.615Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:50.615Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.616Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:50.616Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:50.616Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:50.616Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for templates/404.html
[2025-05-26T09:11:50.617Z] [INFO] 📝 Detected language: html
[2025-05-26T09:11:50.617Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/templates/404.html
[2025-05-26T09:11:50.617Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\templates\404.html
[2025-05-26T09:11:50.618Z] [INFO] 📝 Language: html
[2025-05-26T09:11:50.618Z] [INFO] 📄 Current content length: 3553
[2025-05-26T09:11:50.618Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:11:50.618Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:11:50.619Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:11:50.620Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.621Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:50.621Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:11:50.622Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:11:50.622Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:11:50.623Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:11:50.623Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.624Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:11:50.624Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:11:50.624Z] [INFO] 🌐 Language html supported: false
[2025-05-26T09:11:50.625Z] [WARN] ❌ Semantic analysis not enabled for language: html
[2025-05-26T09:11:50.626Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for templates/404.html
[2025-05-26T09:11:50.626Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:11:50.626Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:11:50.627Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:50.627Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:11:50.627Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:11:50.628Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:11:50.628Z] [INFO] Getting contextual info for: templates/404.html
[2025-05-26T09:11:50.893Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- templates/404.html
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:11:51.264Z] [INFO] Contextual analysis completed for: templates/404.html
[2025-05-26T09:11:51.264Z] [INFO] Contextual analysis completed for templates/404.html
[2025-05-26T09:11:51.265Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:51.265Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:51.265Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:51.266Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:51.267Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:51.267Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:51.267Z] [INFO] Generating AI documentation for: templates/404.html
[2025-05-26T09:11:51.268Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:11:51.268Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:11:51.268Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:51.269Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:11:51.269Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:11:51.270Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:11:51.270Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:11:51.270Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:11:51.271Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:11:51.272Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:11:51.275Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:11:51.276Z] [WARN] Failed to generate AI documentation for: templates/404.html
[2025-05-26T09:11:51.276Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 4c3b04664f0996929ccb921f7bbfd633
[2025-05-26T09:11:51.277Z] [INFO] 📄 File: templates/404.html
[2025-05-26T09:11:51.277Z] [INFO] 🔄 Event type: create
[2025-05-26T09:11:51.278Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:11:51.288Z] [INFO] ✅ SAVED documentation record: 4c3b04664f0996929ccb921f7bbfd633 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\4c3b04664f0996929ccb921f7bbfd633.json
[2025-05-26T09:11:51.296Z] [INFO] Successfully processed change: 4c3b04664f0996929ccb921f7bbfd633
[2025-05-26T09:12:05.034Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:05.035Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.036Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:05.037Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:05.038Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:05.039Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.039Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:05.040Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:05.060Z] [INFO] Directory created: static (skipping content analysis)
[2025-05-26T09:12:05.061Z] [INFO] File created: static/style.css
[2025-05-26T09:12:05.061Z] [INFO] Processing change: create - static/style.css
[2025-05-26T09:12:05.062Z] [INFO] Generated diff for: static/style.css
[2025-05-26T09:12:05.062Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for static/style.css
[2025-05-26T09:12:05.063Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:12:05.063Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:05.063Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:05.063Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.064Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:05.064Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:05.065Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:05.065Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:12:05.065Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:12:05.065Z] [INFO]   - Event type: create
[2025-05-26T09:12:05.066Z] [INFO]   - Has current content: true
[2025-05-26T09:12:05.066Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:05.066Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:05.067Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.067Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:05.068Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:05.068Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:05.068Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for static/style.css
[2025-05-26T09:12:05.069Z] [INFO] 📝 Detected language: css
[2025-05-26T09:12:05.069Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/static/style.css
[2025-05-26T09:12:05.070Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\static\style.css
[2025-05-26T09:12:05.070Z] [INFO] 📝 Language: css
[2025-05-26T09:12:05.070Z] [INFO] 📄 Current content length: 1918
[2025-05-26T09:12:05.071Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:12:05.071Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:05.071Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:05.072Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.072Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:05.073Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:05.073Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:05.073Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:12:05.074Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:12:05.074Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.074Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:12:05.075Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:12:05.075Z] [INFO] 🌐 Language css supported: false
[2025-05-26T09:12:05.075Z] [WARN] ❌ Semantic analysis not enabled for language: css
[2025-05-26T09:12:05.076Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for static/style.css
[2025-05-26T09:12:05.077Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:12:05.077Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:12:05.077Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.078Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:05.078Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:12:05.078Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:12:05.079Z] [INFO] Getting contextual info for: static/style.css
[2025-05-26T09:12:05.266Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- static/style.css
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:12:05.490Z] [INFO] Contextual analysis completed for: static/style.css
[2025-05-26T09:12:05.491Z] [INFO] Contextual analysis completed for static/style.css
[2025-05-26T09:12:05.491Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:05.491Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:05.491Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.492Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:05.492Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:05.493Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:05.493Z] [INFO] Generating AI documentation for: static/style.css
[2025-05-26T09:12:05.493Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:05.493Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:05.494Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.494Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:05.494Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:05.495Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:05.495Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:12:05.495Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:05.496Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:12:05.496Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:12:05.501Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:12:05.501Z] [WARN] Failed to generate AI documentation for: static/style.css
[2025-05-26T09:12:05.501Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: d5ee566d649a8879e290daa33c097486
[2025-05-26T09:12:05.502Z] [INFO] 📄 File: static/style.css
[2025-05-26T09:12:05.502Z] [INFO] 🔄 Event type: create
[2025-05-26T09:12:05.503Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:12:05.507Z] [INFO] ✅ SAVED documentation record: d5ee566d649a8879e290daa33c097486 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\d5ee566d649a8879e290daa33c097486.json
[2025-05-26T09:12:05.511Z] [INFO] Successfully processed change: d5ee566d649a8879e290daa33c097486
[2025-05-26T09:12:17.200Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:17.201Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.202Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:17.202Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:17.207Z] [INFO] Directory created: examples (skipping content analysis)
[2025-05-26T09:12:17.297Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:17.298Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.298Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:17.299Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:17.302Z] [INFO] File created: examples/01_hello_world.py
[2025-05-26T09:12:17.303Z] [INFO] Processing change: create - examples/01_hello_world.py
[2025-05-26T09:12:17.304Z] [INFO] Generated diff for: examples/01_hello_world.py
[2025-05-26T09:12:17.305Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for examples/01_hello_world.py
[2025-05-26T09:12:17.306Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:12:17.306Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:17.307Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:17.307Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.308Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:17.308Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:17.308Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:17.309Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:12:17.309Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:12:17.309Z] [INFO]   - Event type: create
[2025-05-26T09:12:17.310Z] [INFO]   - Has current content: true
[2025-05-26T09:12:17.310Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:17.310Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:17.311Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.311Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:17.311Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:17.312Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:17.312Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for examples/01_hello_world.py
[2025-05-26T09:12:17.312Z] [INFO] 📝 Detected language: python
[2025-05-26T09:12:17.313Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/01_hello_world.py
[2025-05-26T09:12:17.313Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\01_hello_world.py
[2025-05-26T09:12:17.313Z] [INFO] 📝 Language: python
[2025-05-26T09:12:17.314Z] [INFO] 📄 Current content length: 1616
[2025-05-26T09:12:17.314Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:12:17.314Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:17.315Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:17.315Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.316Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:17.317Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:17.317Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:17.318Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:12:17.318Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:12:17.318Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.319Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:12:17.320Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:12:17.321Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:12:17.321Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\01_hello_world.py
[2025-05-26T09:12:17.321Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:12:17.322Z] [INFO] 🔍 Getting document symbols for python (1616 chars)
[2025-05-26T09:12:17.322Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/01_hello_world.py
[2025-05-26T09:12:17.323Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:12:17.335Z] [INFO] 📊 LSP returned 4 symbols from open document
[2025-05-26T09:12:17.336Z] [INFO] ✅ Found 4 current symbols
[2025-05-26T09:12:17.337Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:12:17.337Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:12:17.338Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:12:17.338Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 4
[2025-05-26T09:12:17.339Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:12:17.339Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:12:17.339Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:17.340Z] [INFO]       Range: 11:0-11:3
[2025-05-26T09:12:17.340Z] [INFO]       Selection: 11:0-11:3
[2025-05-26T09:12:17.341Z] [INFO]   2. hello_world (Kind: Function, Detail: 'none')
[2025-05-26T09:12:17.341Z] [INFO]       Range: 14:0-17:59
[2025-05-26T09:12:17.343Z] [INFO]       Selection: 15:4-15:15
[2025-05-26T09:12:17.343Z] [INFO]   3. welcome (Kind: Function, Detail: 'none')
[2025-05-26T09:12:17.343Z] [INFO]       Range: 20:0-23:83
[2025-05-26T09:12:17.344Z] [INFO]       Selection: 21:4-21:11
[2025-05-26T09:12:17.344Z] [INFO]   4. formatted (Kind: Function, Detail: 'none')
[2025-05-26T09:12:17.345Z] [INFO]       Range: 26:0-42:7
[2025-05-26T09:12:17.346Z] [INFO]       Selection: 27:4-27:13
[2025-05-26T09:12:17.346Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:12:17.346Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.347Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:12:17.348Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:12:17.348Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:12:17.349Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:12:17.350Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 4 symbols
[2025-05-26T09:12:17.350Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:12:17.350Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (4 entries):
[2025-05-26T09:12:17.351Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:12:17.351Z] [INFO]   "hello_world:11" -> hello_world (Function, Detail: 'none')
[2025-05-26T09:12:17.351Z] [INFO]   "welcome:11" -> welcome (Function, Detail: 'none')
[2025-05-26T09:12:17.352Z] [INFO]   "formatted:11" -> formatted (Function, Detail: 'none')
[2025-05-26T09:12:17.352Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:12:17.352Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:12:17.353Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'hello_world:11' -> hello_world (Kind: 11)
[2025-05-26T09:12:17.353Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'welcome:11' -> welcome (Kind: 11)
[2025-05-26T09:12:17.353Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'formatted:11' -> formatted (Kind: 11)
[2025-05-26T09:12:17.354Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:12:17.354Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:12:17.355Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:12:17.355Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.356Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:12:17.356Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:12:17.357Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:12:17.357Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 4 primitives:
[2025-05-26T09:12:17.358Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:17.358Z] [INFO]   2. ADD function: 'hello_world'
[2025-05-26T09:12:17.358Z] [INFO]   3. ADD function: 'welcome'
[2025-05-26T09:12:17.359Z] [INFO]   4. ADD function: 'formatted'
[2025-05-26T09:12:17.360Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:12:17.361Z] [INFO] 🎯 Generated 4 semantic primitives
[2025-05-26T09:12:17.361Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:17.362Z] [INFO]   2. ADD function: 'hello_world'
[2025-05-26T09:12:17.362Z] [INFO]   3. ADD function: 'welcome'
[2025-05-26T09:12:17.362Z] [INFO]   4. ADD function: 'formatted'
[2025-05-26T09:12:17.363Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:12:17.363Z] [INFO] 📊 Summary: +4 -0 ~0 ↔0
[2025-05-26T09:12:17.363Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 4 primitives found
[2025-05-26T09:12:17.365Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for examples/01_hello_world.py: 4 primitives found
[2025-05-26T09:12:17.365Z] [INFO] 📊 Primitives stored in record: 4
[2025-05-26T09:12:17.366Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:12:17.366Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:12:17.366Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:17.367Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:17.367Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:12:17.368Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:12:17.368Z] [INFO] Getting contextual info for: examples/01_hello_world.py
[2025-05-26T09:12:17.697Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- examples/01_hello_world.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:12:18.050Z] [INFO] Contextual analysis completed for: examples/01_hello_world.py
[2025-05-26T09:12:18.050Z] [INFO] Contextual analysis completed for examples/01_hello_world.py
[2025-05-26T09:12:18.050Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:18.051Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:18.051Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:18.051Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:18.052Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:18.052Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:18.053Z] [INFO] Generating AI documentation for: examples/01_hello_world.py
[2025-05-26T09:12:18.053Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:18.054Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:18.054Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:18.055Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:18.056Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:18.056Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:18.057Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:12:18.057Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:18.058Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:12:18.058Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:12:18.062Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:12:18.063Z] [WARN] Failed to generate AI documentation for: examples/01_hello_world.py
[2025-05-26T09:12:18.063Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 17517643d1dc4872f042f835a024aa09
[2025-05-26T09:12:18.064Z] [INFO] 📄 File: examples/01_hello_world.py
[2025-05-26T09:12:18.064Z] [INFO] 🔄 Event type: create
[2025-05-26T09:12:18.064Z] [INFO] 🔧 Semantic primitives: 4
[2025-05-26T09:12:18.065Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:12:18.065Z] [INFO]   2. add function: 'hello_world'
[2025-05-26T09:12:18.065Z] [INFO]   3. add function: 'welcome'
[2025-05-26T09:12:18.066Z] [INFO]   4. add function: 'formatted'
[2025-05-26T09:12:18.071Z] [INFO] ✅ SAVED documentation record: 17517643d1dc4872f042f835a024aa09 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\17517643d1dc4872f042f835a024aa09.json
[2025-05-26T09:12:18.076Z] [INFO] Successfully processed change: 17517643d1dc4872f042f835a024aa09
[2025-05-26T09:12:39.521Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:39.522Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.523Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:39.523Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:39.528Z] [INFO] File created: examples/02_routing.py
[2025-05-26T09:12:39.529Z] [INFO] Processing change: create - examples/02_routing.py
[2025-05-26T09:12:39.529Z] [INFO] Generated diff for: examples/02_routing.py
[2025-05-26T09:12:39.530Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for examples/02_routing.py
[2025-05-26T09:12:39.530Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:12:39.530Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:39.531Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:39.531Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.531Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:39.532Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:39.532Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:39.532Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:12:39.533Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:12:39.533Z] [INFO]   - Event type: create
[2025-05-26T09:12:39.533Z] [INFO]   - Has current content: true
[2025-05-26T09:12:39.533Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:39.533Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:39.534Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.534Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:39.534Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:39.535Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:39.535Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for examples/02_routing.py
[2025-05-26T09:12:39.535Z] [INFO] 📝 Detected language: python
[2025-05-26T09:12:39.536Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/02_routing.py
[2025-05-26T09:12:39.536Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\02_routing.py
[2025-05-26T09:12:39.537Z] [INFO] 📝 Language: python
[2025-05-26T09:12:39.537Z] [INFO] 📄 Current content length: 3993
[2025-05-26T09:12:39.537Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:12:39.538Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:39.538Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:39.538Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.539Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:39.539Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:39.539Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:39.540Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:12:39.540Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:12:39.540Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.541Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:12:39.541Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:12:39.541Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:12:39.542Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\02_routing.py
[2025-05-26T09:12:39.542Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:12:39.542Z] [INFO] 🔍 Getting document symbols for python (3993 chars)
[2025-05-26T09:12:39.543Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/02_routing.py
[2025-05-26T09:12:39.543Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:12:39.551Z] [INFO] 📊 LSP returned 12 symbols from open document
[2025-05-26T09:12:39.551Z] [INFO] ✅ Found 12 current symbols
[2025-05-26T09:12:39.551Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:12:39.551Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:12:39.552Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:12:39.552Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 12
[2025-05-26T09:12:39.552Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:12:39.553Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:12:39.553Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.553Z] [INFO]       Range: 10:0-10:3
[2025-05-26T09:12:39.554Z] [INFO]       Selection: 10:0-10:3
[2025-05-26T09:12:39.554Z] [INFO]   2. home (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.554Z] [INFO]       Range: 13:0-26:7
[2025-05-26T09:12:39.555Z] [INFO]       Selection: 14:4-14:8
[2025-05-26T09:12:39.555Z] [INFO]   3. about (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.555Z] [INFO]       Range: 28:0-30:95
[2025-05-26T09:12:39.555Z] [INFO]       Selection: 29:4-29:9
[2025-05-26T09:12:39.556Z] [INFO]   4. show_user_profile (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.556Z] [INFO]       Range: 33:0-41:7
[2025-05-26T09:12:39.556Z] [INFO]       Selection: 34:4-34:21
[2025-05-26T09:12:39.556Z] [INFO]     1. username (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.557Z] [INFO]         Range: 34:22-34:30
[2025-05-26T09:12:39.557Z] [INFO]   5. show_post (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.557Z] [INFO]       Range: 44:0-52:7
[2025-05-26T09:12:39.557Z] [INFO]       Selection: 45:4-45:13
[2025-05-26T09:12:39.558Z] [INFO]     1. post_id (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.558Z] [INFO]         Range: 45:14-45:21
[2025-05-26T09:12:39.558Z] [INFO]   6. show_price (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.558Z] [INFO]       Range: 55:0-63:7
[2025-05-26T09:12:39.559Z] [INFO]       Selection: 56:4-56:14
[2025-05-26T09:12:39.559Z] [INFO]     1. price (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.559Z] [INFO]         Range: 56:15-56:20
[2025-05-26T09:12:39.560Z] [INFO]   7. show_file (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.560Z] [INFO]       Range: 66:0-74:7
[2025-05-26T09:12:39.560Z] [INFO]       Selection: 67:4-67:13
[2025-05-26T09:12:39.561Z] [INFO]     1. filename (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.561Z] [INFO]         Range: 67:14-67:22
[2025-05-26T09:12:39.561Z] [INFO]   8. hello (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.562Z] [INFO]       Range: 77:0-84:96
[2025-05-26T09:12:39.562Z] [INFO]       Selection: 79:4-79:9
[2025-05-26T09:12:39.562Z] [INFO]     1. name (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.563Z] [INFO]         Range: 79:10-79:19
[2025-05-26T09:12:39.563Z] [INFO]   9. user_post (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.563Z] [INFO]       Range: 87:0-96:7
[2025-05-26T09:12:39.563Z] [INFO]       Selection: 88:4-88:13
[2025-05-26T09:12:39.564Z] [INFO]     1. username (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.565Z] [INFO]         Range: 88:14-88:22
[2025-05-26T09:12:39.565Z] [INFO]     2. post_id (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.565Z] [INFO]         Range: 88:24-88:31
[2025-05-26T09:12:39.565Z] [INFO]   10. folder_with_slash (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.566Z] [INFO]       Range: 99:0-107:7
[2025-05-26T09:12:39.566Z] [INFO]       Selection: 100:4-100:21
[2025-05-26T09:12:39.566Z] [INFO]   11. file_without_slash (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.566Z] [INFO]       Range: 109:0-117:7
[2025-05-26T09:12:39.567Z] [INFO]       Selection: 110:4-110:22
[2025-05-26T09:12:39.567Z] [INFO]   12. page_not_found (Kind: Function, Detail: 'none')
[2025-05-26T09:12:39.567Z] [INFO]       Range: 120:0-127:12
[2025-05-26T09:12:39.567Z] [INFO]       Selection: 121:4-121:18
[2025-05-26T09:12:39.567Z] [INFO]     1. error (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:39.568Z] [INFO]         Range: 121:19-121:24
[2025-05-26T09:12:39.568Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:12:39.568Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.569Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:12:39.569Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:12:39.570Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:12:39.570Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:12:39.570Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 20 symbols
[2025-05-26T09:12:39.570Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:12:39.571Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (20 entries):
[2025-05-26T09:12:39.571Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:12:39.572Z] [INFO]   "home:11" -> home (Function, Detail: 'none')
[2025-05-26T09:12:39.572Z] [INFO]   "about:11" -> about (Function, Detail: 'none')
[2025-05-26T09:12:39.572Z] [INFO]   "show_user_profile:11" -> show_user_profile (Function, Detail: 'none')
[2025-05-26T09:12:39.573Z] [INFO]   "show_user_profile.username:12" -> username (Variable, Detail: 'none')
[2025-05-26T09:12:39.573Z] [INFO]   "show_post:11" -> show_post (Function, Detail: 'none')
[2025-05-26T09:12:39.573Z] [INFO]   "show_post.post_id:12" -> post_id (Variable, Detail: 'none')
[2025-05-26T09:12:39.574Z] [INFO]   "show_price:11" -> show_price (Function, Detail: 'none')
[2025-05-26T09:12:39.574Z] [INFO]   "show_price.price:12" -> price (Variable, Detail: 'none')
[2025-05-26T09:12:39.574Z] [INFO]   "show_file:11" -> show_file (Function, Detail: 'none')
[2025-05-26T09:12:39.575Z] [INFO]   "show_file.filename:12" -> filename (Variable, Detail: 'none')
[2025-05-26T09:12:39.575Z] [INFO]   "hello:11" -> hello (Function, Detail: 'none')
[2025-05-26T09:12:39.575Z] [INFO]   "hello.name:12" -> name (Variable, Detail: 'none')
[2025-05-26T09:12:39.576Z] [INFO]   "user_post:11" -> user_post (Function, Detail: 'none')
[2025-05-26T09:12:39.576Z] [INFO]   "user_post.username:12" -> username (Variable, Detail: 'none')
[2025-05-26T09:12:39.576Z] [INFO]   "user_post.post_id:12" -> post_id (Variable, Detail: 'none')
[2025-05-26T09:12:39.576Z] [INFO]   "folder_with_slash:11" -> folder_with_slash (Function, Detail: 'none')
[2025-05-26T09:12:39.577Z] [INFO]   "file_without_slash:11" -> file_without_slash (Function, Detail: 'none')
[2025-05-26T09:12:39.577Z] [INFO]   "page_not_found:11" -> page_not_found (Function, Detail: 'none')
[2025-05-26T09:12:39.577Z] [INFO]   "page_not_found.error:12" -> error (Variable, Detail: 'none')
[2025-05-26T09:12:39.578Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:12:39.578Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:12:39.578Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T09:12:39.578Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T09:12:39.579Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_user_profile:11' -> show_user_profile (Kind: 11)
[2025-05-26T09:12:39.579Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_user_profile.username:12' -> username (Kind: 12)
[2025-05-26T09:12:39.579Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_post:11' -> show_post (Kind: 11)
[2025-05-26T09:12:39.580Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_post.post_id:12' -> post_id (Kind: 12)
[2025-05-26T09:12:39.580Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_price:11' -> show_price (Kind: 11)
[2025-05-26T09:12:39.580Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_price.price:12' -> price (Kind: 12)
[2025-05-26T09:12:39.580Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_file:11' -> show_file (Kind: 11)
[2025-05-26T09:12:39.581Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_file.filename:12' -> filename (Kind: 12)
[2025-05-26T09:12:39.581Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'hello:11' -> hello (Kind: 11)
[2025-05-26T09:12:39.581Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'hello.name:12' -> name (Kind: 12)
[2025-05-26T09:12:39.581Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post:11' -> user_post (Kind: 11)
[2025-05-26T09:12:39.582Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post.username:12' -> username (Kind: 12)
[2025-05-26T09:12:39.582Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post.post_id:12' -> post_id (Kind: 12)
[2025-05-26T09:12:39.582Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'folder_with_slash:11' -> folder_with_slash (Kind: 11)
[2025-05-26T09:12:39.583Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'file_without_slash:11' -> file_without_slash (Kind: 11)
[2025-05-26T09:12:39.583Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found:11' -> page_not_found (Kind: 11)
[2025-05-26T09:12:39.583Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found.error:12' -> error (Kind: 12)
[2025-05-26T09:12:39.584Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:12:39.584Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:12:39.584Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:12:39.585Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.585Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:12:39.586Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:12:39.586Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:12:39.587Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 20 primitives:
[2025-05-26T09:12:39.588Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:39.588Z] [INFO]   2. ADD function: 'home'
[2025-05-26T09:12:39.589Z] [INFO]   3. ADD function: 'about'
[2025-05-26T09:12:39.589Z] [INFO]   4. ADD function: 'show_user_profile'
[2025-05-26T09:12:39.589Z] [INFO]   5. ADD variable: 'username'
[2025-05-26T09:12:39.589Z] [INFO]   6. ADD function: 'show_post'
[2025-05-26T09:12:39.590Z] [INFO]   7. ADD variable: 'post_id'
[2025-05-26T09:12:39.590Z] [INFO]   8. ADD function: 'show_price'
[2025-05-26T09:12:39.590Z] [INFO]   9. ADD variable: 'price'
[2025-05-26T09:12:39.591Z] [INFO]   10. ADD function: 'show_file'
[2025-05-26T09:12:39.591Z] [INFO]   11. ADD variable: 'filename'
[2025-05-26T09:12:39.591Z] [INFO]   12. ADD function: 'hello'
[2025-05-26T09:12:39.592Z] [INFO]   13. ADD variable: 'name'
[2025-05-26T09:12:39.592Z] [INFO]   14. ADD function: 'user_post'
[2025-05-26T09:12:39.592Z] [INFO]   15. ADD variable: 'username'
[2025-05-26T09:12:39.592Z] [INFO]   16. ADD variable: 'post_id'
[2025-05-26T09:12:39.593Z] [INFO]   17. ADD function: 'folder_with_slash'
[2025-05-26T09:12:39.593Z] [INFO]   18. ADD function: 'file_without_slash'
[2025-05-26T09:12:39.593Z] [INFO]   19. ADD function: 'page_not_found'
[2025-05-26T09:12:39.594Z] [INFO]   20. ADD variable: 'error'
[2025-05-26T09:12:39.595Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:12:39.595Z] [INFO] 🎯 Generated 20 semantic primitives
[2025-05-26T09:12:39.596Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:39.597Z] [INFO]   2. ADD function: 'home'
[2025-05-26T09:12:39.597Z] [INFO]   3. ADD function: 'about'
[2025-05-26T09:12:39.597Z] [INFO]   4. ADD function: 'show_user_profile'
[2025-05-26T09:12:39.598Z] [INFO]   5. ADD variable: 'username'
[2025-05-26T09:12:39.598Z] [INFO]   6. ADD function: 'show_post'
[2025-05-26T09:12:39.598Z] [INFO]   7. ADD variable: 'post_id'
[2025-05-26T09:12:39.599Z] [INFO]   8. ADD function: 'show_price'
[2025-05-26T09:12:39.599Z] [INFO]   9. ADD variable: 'price'
[2025-05-26T09:12:39.600Z] [INFO]   10. ADD function: 'show_file'
[2025-05-26T09:12:39.601Z] [INFO]   11. ADD variable: 'filename'
[2025-05-26T09:12:39.601Z] [INFO]   12. ADD function: 'hello'
[2025-05-26T09:12:39.602Z] [INFO]   13. ADD variable: 'name'
[2025-05-26T09:12:39.602Z] [INFO]   14. ADD function: 'user_post'
[2025-05-26T09:12:39.603Z] [INFO]   15. ADD variable: 'username'
[2025-05-26T09:12:39.604Z] [INFO]   16. ADD variable: 'post_id'
[2025-05-26T09:12:39.604Z] [INFO]   17. ADD function: 'folder_with_slash'
[2025-05-26T09:12:39.604Z] [INFO]   18. ADD function: 'file_without_slash'
[2025-05-26T09:12:39.605Z] [INFO]   19. ADD function: 'page_not_found'
[2025-05-26T09:12:39.606Z] [INFO]   20. ADD variable: 'error'
[2025-05-26T09:12:39.606Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:12:39.607Z] [INFO] 📊 Summary: +20 -0 ~0 ↔0
[2025-05-26T09:12:39.608Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 20 primitives found
[2025-05-26T09:12:39.611Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for examples/02_routing.py: 20 primitives found
[2025-05-26T09:12:39.612Z] [INFO] 📊 Primitives stored in record: 20
[2025-05-26T09:12:39.612Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:12:39.613Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:12:39.613Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.614Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:39.614Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:12:39.615Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:12:39.615Z] [INFO] Getting contextual info for: examples/02_routing.py
[2025-05-26T09:12:39.629Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:12:39.639Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:39.642Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:12:39.643Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:12:40.143Z] [INFO] External file change: examples/02_routing.py
[2025-05-26T09:12:40.147Z] [INFO] Processing change: modify - examples/02_routing.py
[2025-05-26T09:12:40.148Z] [INFO] Getting previous version for: examples/02_routing.py
[2025-05-26T09:12:40.226Z] [WARN] No previous version found for: examples/02_routing.py
[2025-05-26T09:12:40.228Z] [INFO] Generated diff for: examples/02_routing.py
[2025-05-26T09:12:40.228Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for examples/02_routing.py
[2025-05-26T09:12:40.228Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:12:40.229Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:40.229Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:40.229Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.230Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:40.230Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:40.231Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:40.231Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:12:40.231Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:12:40.232Z] [INFO]   - Event type: modify
[2025-05-26T09:12:40.232Z] [INFO]   - Has current content: true
[2025-05-26T09:12:40.232Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:40.232Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:40.233Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.233Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:40.233Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:40.234Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:40.234Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for examples/02_routing.py
[2025-05-26T09:12:40.234Z] [INFO] 📝 Detected language: python
[2025-05-26T09:12:40.235Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/02_routing.py
[2025-05-26T09:12:40.235Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\02_routing.py
[2025-05-26T09:12:40.235Z] [INFO] 📝 Language: python
[2025-05-26T09:12:40.236Z] [INFO] 📄 Current content length: 3993
[2025-05-26T09:12:40.237Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:12:40.237Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:12:40.238Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:12:40.238Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.239Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:40.239Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:12:40.240Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:12:40.240Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:12:40.240Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:12:40.240Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.241Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:12:40.241Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:12:40.242Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:12:40.242Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\02_routing.py
[2025-05-26T09:12:40.242Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:12:40.243Z] [INFO] 🔍 Getting document symbols for python (3993 chars)
[2025-05-26T09:12:40.243Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/02_routing.py
[2025-05-26T09:12:40.244Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:12:40.246Z] [INFO] 📊 LSP returned 12 symbols from open document
[2025-05-26T09:12:40.247Z] [INFO] ✅ Found 12 current symbols
[2025-05-26T09:12:40.247Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:12:40.247Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:12:40.248Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:12:40.248Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 12
[2025-05-26T09:12:40.248Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:12:40.248Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:12:40.248Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.249Z] [INFO]       Range: 10:0-10:3
[2025-05-26T09:12:40.249Z] [INFO]       Selection: 10:0-10:3
[2025-05-26T09:12:40.249Z] [INFO]   2. home (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.250Z] [INFO]       Range: 13:0-26:7
[2025-05-26T09:12:40.250Z] [INFO]       Selection: 14:4-14:8
[2025-05-26T09:12:40.250Z] [INFO]   3. about (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.250Z] [INFO]       Range: 28:0-30:95
[2025-05-26T09:12:40.251Z] [INFO]       Selection: 29:4-29:9
[2025-05-26T09:12:40.251Z] [INFO]   4. show_user_profile (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.251Z] [INFO]       Range: 33:0-41:7
[2025-05-26T09:12:40.251Z] [INFO]       Selection: 34:4-34:21
[2025-05-26T09:12:40.251Z] [INFO]     1. username (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.252Z] [INFO]         Range: 34:22-34:30
[2025-05-26T09:12:40.252Z] [INFO]   5. show_post (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.253Z] [INFO]       Range: 44:0-52:7
[2025-05-26T09:12:40.253Z] [INFO]       Selection: 45:4-45:13
[2025-05-26T09:12:40.254Z] [INFO]     1. post_id (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.254Z] [INFO]         Range: 45:14-45:21
[2025-05-26T09:12:40.254Z] [INFO]   6. show_price (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.256Z] [INFO]       Range: 55:0-63:7
[2025-05-26T09:12:40.256Z] [INFO]       Selection: 56:4-56:14
[2025-05-26T09:12:40.256Z] [INFO]     1. price (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.257Z] [INFO]         Range: 56:15-56:20
[2025-05-26T09:12:40.257Z] [INFO]   7. show_file (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.257Z] [INFO]       Range: 66:0-74:7
[2025-05-26T09:12:40.258Z] [INFO]       Selection: 67:4-67:13
[2025-05-26T09:12:40.258Z] [INFO]     1. filename (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.259Z] [INFO]         Range: 67:14-67:22
[2025-05-26T09:12:40.259Z] [INFO]   8. hello (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.259Z] [INFO]       Range: 77:0-84:96
[2025-05-26T09:12:40.260Z] [INFO]       Selection: 79:4-79:9
[2025-05-26T09:12:40.260Z] [INFO]     1. name (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.260Z] [INFO]         Range: 79:10-79:19
[2025-05-26T09:12:40.261Z] [INFO]   9. user_post (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.261Z] [INFO]       Range: 87:0-96:7
[2025-05-26T09:12:40.261Z] [INFO]       Selection: 88:4-88:13
[2025-05-26T09:12:40.262Z] [INFO]     1. username (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.262Z] [INFO]         Range: 88:14-88:22
[2025-05-26T09:12:40.262Z] [INFO]     2. post_id (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.263Z] [INFO]         Range: 88:24-88:31
[2025-05-26T09:12:40.263Z] [INFO]   10. folder_with_slash (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.263Z] [INFO]       Range: 99:0-107:7
[2025-05-26T09:12:40.264Z] [INFO]       Selection: 100:4-100:21
[2025-05-26T09:12:40.264Z] [INFO]   11. file_without_slash (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.264Z] [INFO]       Range: 109:0-117:7
[2025-05-26T09:12:40.264Z] [INFO]       Selection: 110:4-110:22
[2025-05-26T09:12:40.265Z] [INFO]   12. page_not_found (Kind: Function, Detail: 'none')
[2025-05-26T09:12:40.265Z] [INFO]       Range: 120:0-127:12
[2025-05-26T09:12:40.265Z] [INFO]       Selection: 121:4-121:18
[2025-05-26T09:12:40.265Z] [INFO]     1. error (Kind: Variable, Detail: 'none')
[2025-05-26T09:12:40.266Z] [INFO]         Range: 121:19-121:24
[2025-05-26T09:12:40.266Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:12:40.266Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.267Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:12:40.267Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:12:40.267Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:12:40.267Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:12:40.268Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 20 symbols
[2025-05-26T09:12:40.268Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:12:40.268Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (20 entries):
[2025-05-26T09:12:40.269Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:12:40.269Z] [INFO]   "home:11" -> home (Function, Detail: 'none')
[2025-05-26T09:12:40.269Z] [INFO]   "about:11" -> about (Function, Detail: 'none')
[2025-05-26T09:12:40.270Z] [INFO]   "show_user_profile:11" -> show_user_profile (Function, Detail: 'none')
[2025-05-26T09:12:40.270Z] [INFO]   "show_user_profile.username:12" -> username (Variable, Detail: 'none')
[2025-05-26T09:12:40.270Z] [INFO]   "show_post:11" -> show_post (Function, Detail: 'none')
[2025-05-26T09:12:40.271Z] [INFO]   "show_post.post_id:12" -> post_id (Variable, Detail: 'none')
[2025-05-26T09:12:40.271Z] [INFO]   "show_price:11" -> show_price (Function, Detail: 'none')
[2025-05-26T09:12:40.272Z] [INFO]   "show_price.price:12" -> price (Variable, Detail: 'none')
[2025-05-26T09:12:40.272Z] [INFO]   "show_file:11" -> show_file (Function, Detail: 'none')
[2025-05-26T09:12:40.273Z] [INFO]   "show_file.filename:12" -> filename (Variable, Detail: 'none')
[2025-05-26T09:12:40.273Z] [INFO]   "hello:11" -> hello (Function, Detail: 'none')
[2025-05-26T09:12:40.273Z] [INFO]   "hello.name:12" -> name (Variable, Detail: 'none')
[2025-05-26T09:12:40.274Z] [INFO]   "user_post:11" -> user_post (Function, Detail: 'none')
[2025-05-26T09:12:40.274Z] [INFO]   "user_post.username:12" -> username (Variable, Detail: 'none')
[2025-05-26T09:12:40.274Z] [INFO]   "user_post.post_id:12" -> post_id (Variable, Detail: 'none')
[2025-05-26T09:12:40.274Z] [INFO]   "folder_with_slash:11" -> folder_with_slash (Function, Detail: 'none')
[2025-05-26T09:12:40.275Z] [INFO]   "file_without_slash:11" -> file_without_slash (Function, Detail: 'none')
[2025-05-26T09:12:40.275Z] [INFO]   "page_not_found:11" -> page_not_found (Function, Detail: 'none')
[2025-05-26T09:12:40.276Z] [INFO]   "page_not_found.error:12" -> error (Variable, Detail: 'none')
[2025-05-26T09:12:40.276Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:12:40.276Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:12:40.277Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T09:12:40.277Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T09:12:40.277Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_user_profile:11' -> show_user_profile (Kind: 11)
[2025-05-26T09:12:40.277Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_user_profile.username:12' -> username (Kind: 12)
[2025-05-26T09:12:40.278Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_post:11' -> show_post (Kind: 11)
[2025-05-26T09:12:40.278Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_post.post_id:12' -> post_id (Kind: 12)
[2025-05-26T09:12:40.279Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_price:11' -> show_price (Kind: 11)
[2025-05-26T09:12:40.279Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_price.price:12' -> price (Kind: 12)
[2025-05-26T09:12:40.279Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_file:11' -> show_file (Kind: 11)
[2025-05-26T09:12:40.279Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'show_file.filename:12' -> filename (Kind: 12)
[2025-05-26T09:12:40.280Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'hello:11' -> hello (Kind: 11)
[2025-05-26T09:12:40.280Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'hello.name:12' -> name (Kind: 12)
[2025-05-26T09:12:40.280Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post:11' -> user_post (Kind: 11)
[2025-05-26T09:12:40.281Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post.username:12' -> username (Kind: 12)
[2025-05-26T09:12:40.281Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'user_post.post_id:12' -> post_id (Kind: 12)
[2025-05-26T09:12:40.281Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'folder_with_slash:11' -> folder_with_slash (Kind: 11)
[2025-05-26T09:12:40.282Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'file_without_slash:11' -> file_without_slash (Kind: 11)
[2025-05-26T09:12:40.282Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found:11' -> page_not_found (Kind: 11)
[2025-05-26T09:12:40.282Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'page_not_found.error:12' -> error (Kind: 12)
[2025-05-26T09:12:40.282Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:12:40.283Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:12:40.283Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:12:40.283Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.284Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:12:40.284Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:12:40.285Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:12:40.286Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 20 primitives:
[2025-05-26T09:12:40.286Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:40.287Z] [INFO]   2. ADD function: 'home'
[2025-05-26T09:12:40.288Z] [INFO]   3. ADD function: 'about'
[2025-05-26T09:12:40.288Z] [INFO]   4. ADD function: 'show_user_profile'
[2025-05-26T09:12:40.288Z] [INFO]   5. ADD variable: 'username'
[2025-05-26T09:12:40.289Z] [INFO]   6. ADD function: 'show_post'
[2025-05-26T09:12:40.289Z] [INFO]   7. ADD variable: 'post_id'
[2025-05-26T09:12:40.289Z] [INFO]   8. ADD function: 'show_price'
[2025-05-26T09:12:40.290Z] [INFO]   9. ADD variable: 'price'
[2025-05-26T09:12:40.290Z] [INFO]   10. ADD function: 'show_file'
[2025-05-26T09:12:40.290Z] [INFO]   11. ADD variable: 'filename'
[2025-05-26T09:12:40.291Z] [INFO]   12. ADD function: 'hello'
[2025-05-26T09:12:40.291Z] [INFO]   13. ADD variable: 'name'
[2025-05-26T09:12:40.291Z] [INFO]   14. ADD function: 'user_post'
[2025-05-26T09:12:40.292Z] [INFO]   15. ADD variable: 'username'
[2025-05-26T09:12:40.292Z] [INFO]   16. ADD variable: 'post_id'
[2025-05-26T09:12:40.293Z] [INFO]   17. ADD function: 'folder_with_slash'
[2025-05-26T09:12:40.293Z] [INFO]   18. ADD function: 'file_without_slash'
[2025-05-26T09:12:40.294Z] [INFO]   19. ADD function: 'page_not_found'
[2025-05-26T09:12:40.294Z] [INFO]   20. ADD variable: 'error'
[2025-05-26T09:12:40.294Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:12:40.294Z] [INFO] 🎯 Generated 20 semantic primitives
[2025-05-26T09:12:40.295Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:12:40.295Z] [INFO]   2. ADD function: 'home'
[2025-05-26T09:12:40.295Z] [INFO]   3. ADD function: 'about'
[2025-05-26T09:12:40.296Z] [INFO]   4. ADD function: 'show_user_profile'
[2025-05-26T09:12:40.296Z] [INFO]   5. ADD variable: 'username'
[2025-05-26T09:12:40.296Z] [INFO]   6. ADD function: 'show_post'
[2025-05-26T09:12:40.297Z] [INFO]   7. ADD variable: 'post_id'
[2025-05-26T09:12:40.298Z] [INFO]   8. ADD function: 'show_price'
[2025-05-26T09:12:40.298Z] [INFO]   9. ADD variable: 'price'
[2025-05-26T09:12:40.299Z] [INFO]   10. ADD function: 'show_file'
[2025-05-26T09:12:40.299Z] [INFO]   11. ADD variable: 'filename'
[2025-05-26T09:12:40.300Z] [INFO]   12. ADD function: 'hello'
[2025-05-26T09:12:40.300Z] [INFO]   13. ADD variable: 'name'
[2025-05-26T09:12:40.300Z] [INFO]   14. ADD function: 'user_post'
[2025-05-26T09:12:40.301Z] [INFO]   15. ADD variable: 'username'
[2025-05-26T09:12:40.301Z] [INFO]   16. ADD variable: 'post_id'
[2025-05-26T09:12:40.301Z] [INFO]   17. ADD function: 'folder_with_slash'
[2025-05-26T09:12:40.302Z] [INFO]   18. ADD function: 'file_without_slash'
[2025-05-26T09:12:40.302Z] [INFO]   19. ADD function: 'page_not_found'
[2025-05-26T09:12:40.303Z] [INFO]   20. ADD variable: 'error'
[2025-05-26T09:12:40.304Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:12:40.304Z] [INFO] 📊 Summary: +20 -0 ~0 ↔0
[2025-05-26T09:12:40.304Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 20 primitives found
[2025-05-26T09:12:40.307Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for examples/02_routing.py: 20 primitives found
[2025-05-26T09:12:40.308Z] [INFO] 📊 Primitives stored in record: 20
[2025-05-26T09:12:40.308Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:12:40.308Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:12:40.309Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:40.309Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:12:40.310Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:12:40.310Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:12:40.310Z] [INFO] Getting contextual info for: examples/02_routing.py
[2025-05-26T09:12:40.998Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- examples/02_routing.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:12:41.291Z] [INFO] Contextual analysis completed for: examples/02_routing.py
[2025-05-26T09:12:41.292Z] [INFO] Contextual analysis completed for examples/02_routing.py
[2025-05-26T09:12:41.292Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:41.292Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:41.292Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.293Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:41.293Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:41.293Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:41.294Z] [INFO] Generating AI documentation for: examples/02_routing.py
[2025-05-26T09:12:41.294Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:41.294Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:41.294Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.295Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:41.295Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:41.295Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:41.296Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:12:41.296Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.296Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:12:41.297Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:12:41.302Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:12:41.303Z] [WARN] Failed to generate AI documentation for: examples/02_routing.py
[2025-05-26T09:12:41.304Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: bb9f4c913e9ea78aac591fd9f4262cd3
[2025-05-26T09:12:41.305Z] [INFO] 📄 File: examples/02_routing.py
[2025-05-26T09:12:41.305Z] [INFO] 🔄 Event type: create
[2025-05-26T09:12:41.306Z] [INFO] 🔧 Semantic primitives: 20
[2025-05-26T09:12:41.306Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:12:41.307Z] [INFO]   2. add function: 'home'
[2025-05-26T09:12:41.307Z] [INFO]   3. add function: 'about'
[2025-05-26T09:12:41.307Z] [INFO]   4. add function: 'show_user_profile'
[2025-05-26T09:12:41.308Z] [INFO]   5. add variable: 'username'
[2025-05-26T09:12:41.308Z] [INFO]   6. add function: 'show_post'
[2025-05-26T09:12:41.308Z] [INFO]   7. add variable: 'post_id'
[2025-05-26T09:12:41.309Z] [INFO]   8. add function: 'show_price'
[2025-05-26T09:12:41.309Z] [INFO]   9. add variable: 'price'
[2025-05-26T09:12:41.310Z] [INFO]   10. add function: 'show_file'
[2025-05-26T09:12:41.310Z] [INFO]   11. add variable: 'filename'
[2025-05-26T09:12:41.311Z] [INFO]   12. add function: 'hello'
[2025-05-26T09:12:41.311Z] [INFO]   13. add variable: 'name'
[2025-05-26T09:12:41.311Z] [INFO]   14. add function: 'user_post'
[2025-05-26T09:12:41.312Z] [INFO]   15. add variable: 'username'
[2025-05-26T09:12:41.313Z] [INFO]   16. add variable: 'post_id'
[2025-05-26T09:12:41.313Z] [INFO]   17. add function: 'folder_with_slash'
[2025-05-26T09:12:41.314Z] [INFO]   18. add function: 'file_without_slash'
[2025-05-26T09:12:41.314Z] [INFO]   19. add function: 'page_not_found'
[2025-05-26T09:12:41.315Z] [INFO]   20. add variable: 'error'
[2025-05-26T09:12:41.329Z] [INFO] ✅ SAVED documentation record: bb9f4c913e9ea78aac591fd9f4262cd3 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\bb9f4c913e9ea78aac591fd9f4262cd3.json
[2025-05-26T09:12:41.338Z] [INFO] Successfully processed change: bb9f4c913e9ea78aac591fd9f4262cd3
[2025-05-26T09:12:41.628Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- examples/02_routing.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:12:41.840Z] [INFO] Contextual analysis completed for: examples/02_routing.py
[2025-05-26T09:12:41.841Z] [INFO] Contextual analysis completed for examples/02_routing.py
[2025-05-26T09:12:41.841Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:41.841Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:41.842Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.842Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:41.843Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:41.843Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:41.843Z] [INFO] Generating AI documentation for: examples/02_routing.py
[2025-05-26T09:12:41.843Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:12:41.844Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:12:41.844Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.844Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:12:41.845Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:12:41.845Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:12:41.845Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:12:41.845Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:12:41.846Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:12:41.846Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:12:41.849Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:12:41.850Z] [WARN] Failed to generate AI documentation for: examples/02_routing.py
[2025-05-26T09:12:41.850Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 7019d3270035006dbb043d01ee00374f
[2025-05-26T09:12:41.850Z] [INFO] 📄 File: examples/02_routing.py
[2025-05-26T09:12:41.850Z] [INFO] 🔄 Event type: modify
[2025-05-26T09:12:41.851Z] [INFO] 🔧 Semantic primitives: 20
[2025-05-26T09:12:41.851Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:12:41.851Z] [INFO]   2. add function: 'home'
[2025-05-26T09:12:41.852Z] [INFO]   3. add function: 'about'
[2025-05-26T09:12:41.852Z] [INFO]   4. add function: 'show_user_profile'
[2025-05-26T09:12:41.853Z] [INFO]   5. add variable: 'username'
[2025-05-26T09:12:41.853Z] [INFO]   6. add function: 'show_post'
[2025-05-26T09:12:41.853Z] [INFO]   7. add variable: 'post_id'
[2025-05-26T09:12:41.853Z] [INFO]   8. add function: 'show_price'
[2025-05-26T09:12:41.854Z] [INFO]   9. add variable: 'price'
[2025-05-26T09:12:41.854Z] [INFO]   10. add function: 'show_file'
[2025-05-26T09:12:41.854Z] [INFO]   11. add variable: 'filename'
[2025-05-26T09:12:41.855Z] [INFO]   12. add function: 'hello'
[2025-05-26T09:12:41.855Z] [INFO]   13. add variable: 'name'
[2025-05-26T09:12:41.855Z] [INFO]   14. add function: 'user_post'
[2025-05-26T09:12:41.855Z] [INFO]   15. add variable: 'username'
[2025-05-26T09:12:41.856Z] [INFO]   16. add variable: 'post_id'
[2025-05-26T09:12:41.856Z] [INFO]   17. add function: 'folder_with_slash'
[2025-05-26T09:12:41.856Z] [INFO]   18. add function: 'file_without_slash'
[2025-05-26T09:12:41.856Z] [INFO]   19. add function: 'page_not_found'
[2025-05-26T09:12:41.857Z] [INFO]   20. add variable: 'error'
[2025-05-26T09:12:41.862Z] [INFO] ✅ SAVED documentation record: 7019d3270035006dbb043d01ee00374f to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\7019d3270035006dbb043d01ee00374f.json
[2025-05-26T09:12:41.865Z] [INFO] Successfully processed change: 7019d3270035006dbb043d01ee00374f
[2025-05-26T09:13:18.107Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:13:18.108Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.109Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:13:18.110Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:13:18.114Z] [INFO] File created: examples/03_templates.py
[2025-05-26T09:13:18.114Z] [INFO] Processing change: create - examples/03_templates.py
[2025-05-26T09:13:18.115Z] [INFO] Generated diff for: examples/03_templates.py
[2025-05-26T09:13:18.115Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for examples/03_templates.py
[2025-05-26T09:13:18.116Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:13:18.116Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.116Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.117Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.117Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.118Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.118Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.118Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:13:18.119Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:13:18.121Z] [INFO]   - Event type: create
[2025-05-26T09:13:18.121Z] [INFO]   - Has current content: true
[2025-05-26T09:13:18.122Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.122Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.123Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.124Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.124Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.124Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.125Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for examples/03_templates.py
[2025-05-26T09:13:18.125Z] [INFO] 📝 Detected language: python
[2025-05-26T09:13:18.126Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/03_templates.py
[2025-05-26T09:13:18.126Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\03_templates.py
[2025-05-26T09:13:18.127Z] [INFO] 📝 Language: python
[2025-05-26T09:13:18.127Z] [INFO] 📄 Current content length: 7648
[2025-05-26T09:13:18.127Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:13:18.128Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.128Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.129Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.129Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.130Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.130Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.130Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:13:18.131Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:13:18.131Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.132Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:13:18.132Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:13:18.133Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:13:18.133Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\03_templates.py
[2025-05-26T09:13:18.134Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:13:18.134Z] [INFO] 🔍 Getting document symbols for python (7648 chars)
[2025-05-26T09:13:18.134Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/03_templates.py
[2025-05-26T09:13:18.135Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:13:18.152Z] [INFO] 📊 LSP returned 6 symbols from open document
[2025-05-26T09:13:18.155Z] [INFO] ✅ Found 6 current symbols
[2025-05-26T09:13:18.156Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:13:18.156Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:13:18.156Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:13:18.157Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 6
[2025-05-26T09:13:18.158Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:13:18.158Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:13:18.159Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.159Z] [INFO]       Range: 14:0-14:3
[2025-05-26T09:13:18.160Z] [INFO]       Selection: 14:0-14:3
[2025-05-26T09:13:18.160Z] [INFO]   2. BASE_TEMPLATE (Kind: Constant, Detail: 'none')
[2025-05-26T09:13:18.160Z] [INFO]       Range: 17:0-17:13
[2025-05-26T09:13:18.161Z] [INFO]       Selection: 17:0-17:13
[2025-05-26T09:13:18.161Z] [INFO]   3. home (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.161Z] [INFO]       Range: 43:0-71:48
[2025-05-26T09:13:18.162Z] [INFO]       Selection: 44:4-44:8
[2025-05-26T09:13:18.162Z] [INFO]     1. current_time (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.162Z] [INFO]         Range: 46:4-46:16
[2025-05-26T09:13:18.163Z] [INFO]     2. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.163Z] [INFO]         Range: 48:4-48:11
[2025-05-26T09:13:18.163Z] [INFO]   4. about (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.164Z] [INFO]       Range: 73:0-104:48
[2025-05-26T09:13:18.164Z] [INFO]       Selection: 74:4-74:9
[2025-05-26T09:13:18.164Z] [INFO]     1. app_info (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.165Z] [INFO]         Range: 76:4-76:12
[2025-05-26T09:13:18.165Z] [INFO]     2. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.165Z] [INFO]         Range: 83:4-83:11
[2025-05-26T09:13:18.166Z] [INFO]   5. users (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.166Z] [INFO]       Range: 106:0-162:48
[2025-05-26T09:13:18.166Z] [INFO]       Selection: 107:4-107:9
[2025-05-26T09:13:18.166Z] [INFO]     1. users_list (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.167Z] [INFO]         Range: 109:4-109:14
[2025-05-26T09:13:18.167Z] [INFO]     2. users_html (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.167Z] [INFO]         Range: 117:4-117:14
[2025-05-26T09:13:18.168Z] [INFO]     3. user (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.168Z] [INFO]         Range: 120:8-120:12
[2025-05-26T09:13:18.169Z] [INFO]     4. role_color (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.169Z] [INFO]         Range: 121:8-121:18
[2025-05-26T09:13:18.169Z] [INFO]     5. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.170Z] [INFO]         Range: 131:4-131:11
[2025-05-26T09:13:18.170Z] [INFO]   6. products (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.171Z] [INFO]       Range: 164:0-225:48
[2025-05-26T09:13:18.171Z] [INFO]       Selection: 165:4-165:12
[2025-05-26T09:13:18.172Z] [INFO]     1. products (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.172Z] [INFO]         Range: 167:4-167:12
[2025-05-26T09:13:18.172Z] [INFO]     2. products_html (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.173Z] [INFO]         Range: 174:4-174:17
[2025-05-26T09:13:18.173Z] [INFO]     3. product (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.173Z] [INFO]         Range: 176:8-176:15
[2025-05-26T09:13:18.174Z] [INFO]     4. total_value (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.174Z] [INFO]         Range: 186:4-186:15
[2025-05-26T09:13:18.174Z] [INFO]     5. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.175Z] [INFO]         Range: 188:4-188:11
[2025-05-26T09:13:18.175Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:13:18.175Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.176Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:13:18.176Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:13:18.177Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:13:18.177Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:13:18.178Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 20 symbols
[2025-05-26T09:13:18.178Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:13:18.178Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (20 entries):
[2025-05-26T09:13:18.179Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:13:18.179Z] [INFO]   "BASE_TEMPLATE:13" -> BASE_TEMPLATE (Constant, Detail: 'none')
[2025-05-26T09:13:18.179Z] [INFO]   "home:11" -> home (Function, Detail: 'none')
[2025-05-26T09:13:18.180Z] [INFO]   "home.current_time:12" -> current_time (Variable, Detail: 'none')
[2025-05-26T09:13:18.180Z] [INFO]   "home.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.180Z] [INFO]   "about:11" -> about (Function, Detail: 'none')
[2025-05-26T09:13:18.181Z] [INFO]   "about.app_info:12" -> app_info (Variable, Detail: 'none')
[2025-05-26T09:13:18.181Z] [INFO]   "about.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.181Z] [INFO]   "users:11" -> users (Function, Detail: 'none')
[2025-05-26T09:13:18.182Z] [INFO]   "users.users_list:12" -> users_list (Variable, Detail: 'none')
[2025-05-26T09:13:18.182Z] [INFO]   "users.users_html:12" -> users_html (Variable, Detail: 'none')
[2025-05-26T09:13:18.182Z] [INFO]   "users.user:12" -> user (Variable, Detail: 'none')
[2025-05-26T09:13:18.183Z] [INFO]   "users.role_color:12" -> role_color (Variable, Detail: 'none')
[2025-05-26T09:13:18.183Z] [INFO]   "users.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.183Z] [INFO]   "products:11" -> products (Function, Detail: 'none')
[2025-05-26T09:13:18.188Z] [INFO]   "products.products:12" -> products (Variable, Detail: 'none')
[2025-05-26T09:13:18.189Z] [INFO]   "products.products_html:12" -> products_html (Variable, Detail: 'none')
[2025-05-26T09:13:18.190Z] [INFO]   "products.product:12" -> product (Variable, Detail: 'none')
[2025-05-26T09:13:18.190Z] [INFO]   "products.total_value:12" -> total_value (Variable, Detail: 'none')
[2025-05-26T09:13:18.191Z] [INFO]   "products.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.191Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:13:18.191Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:13:18.192Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'BASE_TEMPLATE:13' -> BASE_TEMPLATE (Kind: 13)
[2025-05-26T09:13:18.192Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T09:13:18.192Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home.current_time:12' -> current_time (Kind: 12)
[2025-05-26T09:13:18.193Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.193Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T09:13:18.193Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about.app_info:12' -> app_info (Kind: 12)
[2025-05-26T09:13:18.194Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.210Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users:11' -> users (Kind: 11)
[2025-05-26T09:13:18.211Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.users_list:12' -> users_list (Kind: 12)
[2025-05-26T09:13:18.212Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.users_html:12' -> users_html (Kind: 12)
[2025-05-26T09:13:18.212Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.user:12' -> user (Kind: 12)
[2025-05-26T09:13:18.213Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.role_color:12' -> role_color (Kind: 12)
[2025-05-26T09:13:18.213Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.213Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products:11' -> products (Kind: 11)
[2025-05-26T09:13:18.214Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.products:12' -> products (Kind: 12)
[2025-05-26T09:13:18.214Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.products_html:12' -> products_html (Kind: 12)
[2025-05-26T09:13:18.214Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.product:12' -> product (Kind: 12)
[2025-05-26T09:13:18.215Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.total_value:12' -> total_value (Kind: 12)
[2025-05-26T09:13:18.215Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.215Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:13:18.216Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:13:18.216Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:13:18.216Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.217Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:13:18.217Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:13:18.218Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:13:18.218Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 20 primitives:
[2025-05-26T09:13:18.218Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:13:18.219Z] [INFO]   2. ADD variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:18.219Z] [INFO]   3. ADD function: 'home'
[2025-05-26T09:13:18.220Z] [INFO]   4. ADD variable: 'current_time'
[2025-05-26T09:13:18.220Z] [INFO]   5. ADD variable: 'content'
[2025-05-26T09:13:18.221Z] [INFO]   6. ADD function: 'about'
[2025-05-26T09:13:18.222Z] [INFO]   7. ADD variable: 'app_info'
[2025-05-26T09:13:18.223Z] [INFO]   8. ADD variable: 'content'
[2025-05-26T09:13:18.223Z] [INFO]   9. ADD function: 'users'
[2025-05-26T09:13:18.223Z] [INFO]   10. ADD variable: 'users_list'
[2025-05-26T09:13:18.224Z] [INFO]   11. ADD variable: 'users_html'
[2025-05-26T09:13:18.224Z] [INFO]   12. ADD variable: 'user'
[2025-05-26T09:13:18.224Z] [INFO]   13. ADD variable: 'role_color'
[2025-05-26T09:13:18.224Z] [INFO]   14. ADD variable: 'content'
[2025-05-26T09:13:18.225Z] [INFO]   15. ADD function: 'products'
[2025-05-26T09:13:18.225Z] [INFO]   16. ADD variable: 'products'
[2025-05-26T09:13:18.226Z] [INFO]   17. ADD variable: 'products_html'
[2025-05-26T09:13:18.226Z] [INFO]   18. ADD variable: 'product'
[2025-05-26T09:13:18.226Z] [INFO]   19. ADD variable: 'total_value'
[2025-05-26T09:13:18.227Z] [INFO]   20. ADD variable: 'content'
[2025-05-26T09:13:18.227Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:13:18.228Z] [INFO] 🎯 Generated 20 semantic primitives
[2025-05-26T09:13:18.228Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:13:18.228Z] [INFO]   2. ADD variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:18.229Z] [INFO]   3. ADD function: 'home'
[2025-05-26T09:13:18.234Z] [INFO]   4. ADD variable: 'current_time'
[2025-05-26T09:13:18.235Z] [INFO]   5. ADD variable: 'content'
[2025-05-26T09:13:18.235Z] [INFO]   6. ADD function: 'about'
[2025-05-26T09:13:18.236Z] [INFO]   7. ADD variable: 'app_info'
[2025-05-26T09:13:18.237Z] [INFO]   8. ADD variable: 'content'
[2025-05-26T09:13:18.237Z] [INFO]   9. ADD function: 'users'
[2025-05-26T09:13:18.237Z] [INFO]   10. ADD variable: 'users_list'
[2025-05-26T09:13:18.238Z] [INFO]   11. ADD variable: 'users_html'
[2025-05-26T09:13:18.238Z] [INFO]   12. ADD variable: 'user'
[2025-05-26T09:13:18.238Z] [INFO]   13. ADD variable: 'role_color'
[2025-05-26T09:13:18.238Z] [INFO]   14. ADD variable: 'content'
[2025-05-26T09:13:18.239Z] [INFO]   15. ADD function: 'products'
[2025-05-26T09:13:18.239Z] [INFO]   16. ADD variable: 'products'
[2025-05-26T09:13:18.239Z] [INFO]   17. ADD variable: 'products_html'
[2025-05-26T09:13:18.239Z] [INFO]   18. ADD variable: 'product'
[2025-05-26T09:13:18.239Z] [INFO]   19. ADD variable: 'total_value'
[2025-05-26T09:13:18.240Z] [INFO]   20. ADD variable: 'content'
[2025-05-26T09:13:18.240Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:13:18.240Z] [INFO] 📊 Summary: +20 -0 ~0 ↔0
[2025-05-26T09:13:18.240Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 20 primitives found
[2025-05-26T09:13:18.243Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for examples/03_templates.py: 20 primitives found
[2025-05-26T09:13:18.243Z] [INFO] 📊 Primitives stored in record: 20
[2025-05-26T09:13:18.243Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:13:18.244Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:13:18.244Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.245Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.245Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.245Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.246Z] [INFO] Getting contextual info for: examples/03_templates.py
[2025-05-26T09:13:18.255Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:13:18.256Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.257Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:13:18.258Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:13:18.762Z] [INFO] External file change: examples/03_templates.py
[2025-05-26T09:13:18.763Z] [INFO] Processing change: modify - examples/03_templates.py
[2025-05-26T09:13:18.763Z] [INFO] Getting previous version for: examples/03_templates.py
[2025-05-26T09:13:18.846Z] [WARN] No previous version found for: examples/03_templates.py
[2025-05-26T09:13:18.847Z] [INFO] Generated diff for: examples/03_templates.py
[2025-05-26T09:13:18.848Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for examples/03_templates.py
[2025-05-26T09:13:18.848Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:13:18.848Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.848Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.849Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.849Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.850Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.850Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.850Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:13:18.851Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:13:18.851Z] [INFO]   - Event type: modify
[2025-05-26T09:13:18.851Z] [INFO]   - Has current content: true
[2025-05-26T09:13:18.852Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.852Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.853Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.854Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.854Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.855Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.855Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for examples/03_templates.py
[2025-05-26T09:13:18.856Z] [INFO] 📝 Detected language: python
[2025-05-26T09:13:18.856Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/03_templates.py
[2025-05-26T09:13:18.857Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\03_templates.py
[2025-05-26T09:13:18.857Z] [INFO] 📝 Language: python
[2025-05-26T09:13:18.858Z] [INFO] 📄 Current content length: 7648
[2025-05-26T09:13:18.858Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:13:18.858Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:18.859Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:18.859Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.860Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.860Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.861Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.861Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:13:18.861Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:13:18.862Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.863Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:13:18.863Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:13:18.864Z] [INFO] 🌐 Language python supported: true
[2025-05-26T09:13:18.864Z] [INFO] 🚀 Starting semantic analysis for python file: c:\Users\<USER>\Documents\augment-projects\learn-flask\examples\03_templates.py
[2025-05-26T09:13:18.864Z] [INFO] 📊 Getting current document symbols...
[2025-05-26T09:13:18.865Z] [INFO] 🔍 Getting document symbols for python (7648 chars)
[2025-05-26T09:13:18.865Z] [INFO] 📂 Checking for existing open document: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/examples/03_templates.py
[2025-05-26T09:13:18.865Z] [INFO] ✅ Found matching open document, using it for symbol extraction
[2025-05-26T09:13:18.870Z] [INFO] 📊 LSP returned 6 symbols from open document
[2025-05-26T09:13:18.871Z] [INFO] ✅ Found 6 current symbols
[2025-05-26T09:13:18.871Z] [INFO] 📄 No previous content - treating as new file
[2025-05-26T09:13:18.872Z] [INFO] 🔄 Diffing symbol trees...
[2025-05-26T09:13:18.872Z] [INFO] [DIFF_SYMBOLS] 🔄 Starting symbol tree diffing
[2025-05-26T09:13:18.872Z] [INFO] [DIFF_SYMBOLS] 📊 Old symbols count: 0, New symbols count: 6
[2025-05-26T09:13:18.872Z] [INFO] [DIFF_SYMBOLS] 📋 OLD SYMBOLS (Previous Content):
[2025-05-26T09:13:18.873Z] [INFO] [DIFF_SYMBOLS] 📋 NEW SYMBOLS (Current Content):
[2025-05-26T09:13:18.873Z] [INFO]   1. app (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.873Z] [INFO]       Range: 14:0-14:3
[2025-05-26T09:13:18.873Z] [INFO]       Selection: 14:0-14:3
[2025-05-26T09:13:18.874Z] [INFO]   2. BASE_TEMPLATE (Kind: Constant, Detail: 'none')
[2025-05-26T09:13:18.874Z] [INFO]       Range: 17:0-17:13
[2025-05-26T09:13:18.874Z] [INFO]       Selection: 17:0-17:13
[2025-05-26T09:13:18.874Z] [INFO]   3. home (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.875Z] [INFO]       Range: 43:0-71:48
[2025-05-26T09:13:18.875Z] [INFO]       Selection: 44:4-44:8
[2025-05-26T09:13:18.875Z] [INFO]     1. current_time (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.876Z] [INFO]         Range: 46:4-46:16
[2025-05-26T09:13:18.876Z] [INFO]     2. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.876Z] [INFO]         Range: 48:4-48:11
[2025-05-26T09:13:18.877Z] [INFO]   4. about (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.877Z] [INFO]       Range: 73:0-104:48
[2025-05-26T09:13:18.878Z] [INFO]       Selection: 74:4-74:9
[2025-05-26T09:13:18.878Z] [INFO]     1. app_info (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.878Z] [INFO]         Range: 76:4-76:12
[2025-05-26T09:13:18.879Z] [INFO]     2. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.879Z] [INFO]         Range: 83:4-83:11
[2025-05-26T09:13:18.879Z] [INFO]   5. users (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.880Z] [INFO]       Range: 106:0-162:48
[2025-05-26T09:13:18.880Z] [INFO]       Selection: 107:4-107:9
[2025-05-26T09:13:18.881Z] [INFO]     1. users_list (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.881Z] [INFO]         Range: 109:4-109:14
[2025-05-26T09:13:18.882Z] [INFO]     2. users_html (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.882Z] [INFO]         Range: 117:4-117:14
[2025-05-26T09:13:18.882Z] [INFO]     3. user (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.883Z] [INFO]         Range: 120:8-120:12
[2025-05-26T09:13:18.883Z] [INFO]     4. role_color (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.884Z] [INFO]         Range: 121:8-121:18
[2025-05-26T09:13:18.884Z] [INFO]     5. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.884Z] [INFO]         Range: 131:4-131:11
[2025-05-26T09:13:18.885Z] [INFO]   6. products (Kind: Function, Detail: 'none')
[2025-05-26T09:13:18.885Z] [INFO]       Range: 164:0-225:48
[2025-05-26T09:13:18.885Z] [INFO]       Selection: 165:4-165:12
[2025-05-26T09:13:18.886Z] [INFO]     1. products (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.887Z] [INFO]         Range: 167:4-167:12
[2025-05-26T09:13:18.887Z] [INFO]     2. products_html (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.888Z] [INFO]         Range: 174:4-174:17
[2025-05-26T09:13:18.888Z] [INFO]     3. product (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.889Z] [INFO]         Range: 176:8-176:15
[2025-05-26T09:13:18.889Z] [INFO]     4. total_value (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.889Z] [INFO]         Range: 186:4-186:15
[2025-05-26T09:13:18.890Z] [INFO]     5. content (Kind: Variable, Detail: 'none')
[2025-05-26T09:13:18.890Z] [INFO]         Range: 188:4-188:11
[2025-05-26T09:13:18.890Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.maxNestingDepth'
[2025-05-26T09:13:18.891Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.891Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.maxNestingDepth': {
  "key": "learningDocs.semanticAnalysis.maxNestingDepth",
  "defaultValue": 3
}
[2025-05-26T09:13:18.891Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.maxNestingDepth'): 3 (Type: number)
[2025-05-26T09:13:18.892Z] [INFO] [DIFF_SYMBOLS] 🔧 Max nesting depth: 3
[2025-05-26T09:13:18.892Z] [INFO] [DIFF_SYMBOLS] 🔧 Flattening symbol trees...
[2025-05-26T09:13:18.892Z] [INFO] [DIFF_SYMBOLS] 📊 Flattened - Previous: 0 symbols, Current: 20 symbols
[2025-05-26T09:13:18.893Z] [INFO] [DIFF_SYMBOLS] 🔑 PREVIOUS SYMBOL MAP (0 entries):
[2025-05-26T09:13:18.893Z] [INFO] [DIFF_SYMBOLS] 🔑 CURRENT SYMBOL MAP (20 entries):
[2025-05-26T09:13:18.893Z] [INFO]   "app:12" -> app (Variable, Detail: 'none')
[2025-05-26T09:13:18.894Z] [INFO]   "BASE_TEMPLATE:13" -> BASE_TEMPLATE (Constant, Detail: 'none')
[2025-05-26T09:13:18.894Z] [INFO]   "home:11" -> home (Function, Detail: 'none')
[2025-05-26T09:13:18.894Z] [INFO]   "home.current_time:12" -> current_time (Variable, Detail: 'none')
[2025-05-26T09:13:18.895Z] [INFO]   "home.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.895Z] [INFO]   "about:11" -> about (Function, Detail: 'none')
[2025-05-26T09:13:18.895Z] [INFO]   "about.app_info:12" -> app_info (Variable, Detail: 'none')
[2025-05-26T09:13:18.896Z] [INFO]   "about.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.896Z] [INFO]   "users:11" -> users (Function, Detail: 'none')
[2025-05-26T09:13:18.896Z] [INFO]   "users.users_list:12" -> users_list (Variable, Detail: 'none')
[2025-05-26T09:13:18.896Z] [INFO]   "users.users_html:12" -> users_html (Variable, Detail: 'none')
[2025-05-26T09:13:18.897Z] [INFO]   "users.user:12" -> user (Variable, Detail: 'none')
[2025-05-26T09:13:18.897Z] [INFO]   "users.role_color:12" -> role_color (Variable, Detail: 'none')
[2025-05-26T09:13:18.897Z] [INFO]   "users.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.898Z] [INFO]   "products:11" -> products (Function, Detail: 'none')
[2025-05-26T09:13:18.898Z] [INFO]   "products.products:12" -> products (Variable, Detail: 'none')
[2025-05-26T09:13:18.898Z] [INFO]   "products.products_html:12" -> products_html (Variable, Detail: 'none')
[2025-05-26T09:13:18.899Z] [INFO]   "products.product:12" -> product (Variable, Detail: 'none')
[2025-05-26T09:13:18.899Z] [INFO]   "products.total_value:12" -> total_value (Variable, Detail: 'none')
[2025-05-26T09:13:18.899Z] [INFO]   "products.content:12" -> content (Variable, Detail: 'none')
[2025-05-26T09:13:18.900Z] [INFO] [DIFF_SYMBOLS] ➕ Looking for ADDED symbols...
[2025-05-26T09:13:18.900Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'app:12' -> app (Kind: 12)
[2025-05-26T09:13:18.900Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'BASE_TEMPLATE:13' -> BASE_TEMPLATE (Kind: 13)
[2025-05-26T09:13:18.901Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home:11' -> home (Kind: 11)
[2025-05-26T09:13:18.901Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home.current_time:12' -> current_time (Kind: 12)
[2025-05-26T09:13:18.901Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'home.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.902Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about:11' -> about (Kind: 11)
[2025-05-26T09:13:18.902Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about.app_info:12' -> app_info (Kind: 12)
[2025-05-26T09:13:18.903Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'about.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.903Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users:11' -> users (Kind: 11)
[2025-05-26T09:13:18.904Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.users_list:12' -> users_list (Kind: 12)
[2025-05-26T09:13:18.904Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.users_html:12' -> users_html (Kind: 12)
[2025-05-26T09:13:18.905Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.user:12' -> user (Kind: 12)
[2025-05-26T09:13:18.905Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.role_color:12' -> role_color (Kind: 12)
[2025-05-26T09:13:18.905Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'users.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.906Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products:11' -> products (Kind: 11)
[2025-05-26T09:13:18.906Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.products:12' -> products (Kind: 12)
[2025-05-26T09:13:18.906Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.products_html:12' -> products_html (Kind: 12)
[2025-05-26T09:13:18.907Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.product:12' -> product (Kind: 12)
[2025-05-26T09:13:18.907Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.total_value:12' -> total_value (Kind: 12)
[2025-05-26T09:13:18.907Z] [INFO] [DIFF_SYMBOLS] ✅ FOUND ADD: 'products.content:12' -> content (Kind: 12)
[2025-05-26T09:13:18.908Z] [INFO] [DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...
[2025-05-26T09:13:18.908Z] [INFO] [DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...
[2025-05-26T09:13:18.908Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enableRenameDetection'
[2025-05-26T09:13:18.909Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.909Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enableRenameDetection': {
  "key": "learningDocs.semanticAnalysis.enableRenameDetection",
  "defaultValue": false
}
[2025-05-26T09:13:18.910Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enableRenameDetection'): false (Type: boolean)
[2025-05-26T09:13:18.910Z] [INFO] [DIFF_SYMBOLS] 🔄 Rename detection enabled: false
[2025-05-26T09:13:18.911Z] [INFO] [DIFF_SYMBOLS] 🎯 Generated 20 primitives:
[2025-05-26T09:13:18.911Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:13:18.911Z] [INFO]   2. ADD variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:18.912Z] [INFO]   3. ADD function: 'home'
[2025-05-26T09:13:18.912Z] [INFO]   4. ADD variable: 'current_time'
[2025-05-26T09:13:18.912Z] [INFO]   5. ADD variable: 'content'
[2025-05-26T09:13:18.913Z] [INFO]   6. ADD function: 'about'
[2025-05-26T09:13:18.913Z] [INFO]   7. ADD variable: 'app_info'
[2025-05-26T09:13:18.913Z] [INFO]   8. ADD variable: 'content'
[2025-05-26T09:13:18.914Z] [INFO]   9. ADD function: 'users'
[2025-05-26T09:13:18.914Z] [INFO]   10. ADD variable: 'users_list'
[2025-05-26T09:13:18.915Z] [INFO]   11. ADD variable: 'users_html'
[2025-05-26T09:13:18.916Z] [INFO]   12. ADD variable: 'user'
[2025-05-26T09:13:18.916Z] [INFO]   13. ADD variable: 'role_color'
[2025-05-26T09:13:18.916Z] [INFO]   14. ADD variable: 'content'
[2025-05-26T09:13:18.917Z] [INFO]   15. ADD function: 'products'
[2025-05-26T09:13:18.917Z] [INFO]   16. ADD variable: 'products'
[2025-05-26T09:13:18.917Z] [INFO]   17. ADD variable: 'products_html'
[2025-05-26T09:13:18.917Z] [INFO]   18. ADD variable: 'product'
[2025-05-26T09:13:18.918Z] [INFO]   19. ADD variable: 'total_value'
[2025-05-26T09:13:18.918Z] [INFO]   20. ADD variable: 'content'
[2025-05-26T09:13:18.919Z] [INFO] [DIFF_SYMBOLS] ✅ Symbol tree diffing completed
[2025-05-26T09:13:18.919Z] [INFO] 🎯 Generated 20 semantic primitives
[2025-05-26T09:13:18.920Z] [INFO]   1. ADD variable: 'app'
[2025-05-26T09:13:18.920Z] [INFO]   2. ADD variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:18.921Z] [INFO]   3. ADD function: 'home'
[2025-05-26T09:13:18.921Z] [INFO]   4. ADD variable: 'current_time'
[2025-05-26T09:13:18.921Z] [INFO]   5. ADD variable: 'content'
[2025-05-26T09:13:18.921Z] [INFO]   6. ADD function: 'about'
[2025-05-26T09:13:18.922Z] [INFO]   7. ADD variable: 'app_info'
[2025-05-26T09:13:18.922Z] [INFO]   8. ADD variable: 'content'
[2025-05-26T09:13:18.922Z] [INFO]   9. ADD function: 'users'
[2025-05-26T09:13:18.923Z] [INFO]   10. ADD variable: 'users_list'
[2025-05-26T09:13:18.923Z] [INFO]   11. ADD variable: 'users_html'
[2025-05-26T09:13:18.923Z] [INFO]   12. ADD variable: 'user'
[2025-05-26T09:13:18.923Z] [INFO]   13. ADD variable: 'role_color'
[2025-05-26T09:13:18.924Z] [INFO]   14. ADD variable: 'content'
[2025-05-26T09:13:18.924Z] [INFO]   15. ADD function: 'products'
[2025-05-26T09:13:18.924Z] [INFO]   16. ADD variable: 'products'
[2025-05-26T09:13:18.924Z] [INFO]   17. ADD variable: 'products_html'
[2025-05-26T09:13:18.925Z] [INFO]   18. ADD variable: 'product'
[2025-05-26T09:13:18.925Z] [INFO]   19. ADD variable: 'total_value'
[2025-05-26T09:13:18.925Z] [INFO]   20. ADD variable: 'content'
[2025-05-26T09:13:18.925Z] [INFO] 📈 Analysis confidence: 0.9
[2025-05-26T09:13:18.926Z] [INFO] 📊 Summary: +20 -0 ~0 ↔0
[2025-05-26T09:13:18.926Z] [INFO] ✅ SEMANTIC ANALYSIS COMPLETED: 20 primitives found
[2025-05-26T09:13:18.929Z] [INFO] ✅ SEMANTIC ANALYSIS SUCCESS for examples/03_templates.py: 20 primitives found
[2025-05-26T09:13:18.930Z] [INFO] 📊 Primitives stored in record: 20
[2025-05-26T09:13:18.930Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:13:18.930Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:13:18.931Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:18.931Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:18.931Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:13:18.932Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:13:18.932Z] [INFO] Getting contextual info for: examples/03_templates.py
[2025-05-26T09:13:19.717Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- examples/03_templates.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:13:19.926Z] [INFO] Contextual analysis completed for: examples/03_templates.py
[2025-05-26T09:13:19.927Z] [INFO] Contextual analysis completed for examples/03_templates.py
[2025-05-26T09:13:19.927Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:19.927Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:19.928Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:19.928Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:19.929Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:19.929Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:19.930Z] [INFO] Generating AI documentation for: examples/03_templates.py
[2025-05-26T09:13:19.930Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:19.930Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:19.931Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:19.931Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:19.931Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:19.932Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:19.932Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:13:19.933Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:19.933Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:13:19.934Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:13:19.938Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:13:19.938Z] [WARN] Failed to generate AI documentation for: examples/03_templates.py
[2025-05-26T09:13:19.939Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 820319815e9fde77b06d5b0d5f0d16b7
[2025-05-26T09:13:19.939Z] [INFO] 📄 File: examples/03_templates.py
[2025-05-26T09:13:19.939Z] [INFO] 🔄 Event type: create
[2025-05-26T09:13:19.940Z] [INFO] 🔧 Semantic primitives: 20
[2025-05-26T09:13:19.940Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:13:19.940Z] [INFO]   2. add variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:19.940Z] [INFO]   3. add function: 'home'
[2025-05-26T09:13:19.941Z] [INFO]   4. add variable: 'current_time'
[2025-05-26T09:13:19.941Z] [INFO]   5. add variable: 'content'
[2025-05-26T09:13:19.941Z] [INFO]   6. add function: 'about'
[2025-05-26T09:13:19.941Z] [INFO]   7. add variable: 'app_info'
[2025-05-26T09:13:19.942Z] [INFO]   8. add variable: 'content'
[2025-05-26T09:13:19.942Z] [INFO]   9. add function: 'users'
[2025-05-26T09:13:19.942Z] [INFO]   10. add variable: 'users_list'
[2025-05-26T09:13:19.942Z] [INFO]   11. add variable: 'users_html'
[2025-05-26T09:13:19.943Z] [INFO]   12. add variable: 'user'
[2025-05-26T09:13:19.943Z] [INFO]   13. add variable: 'role_color'
[2025-05-26T09:13:19.943Z] [INFO]   14. add variable: 'content'
[2025-05-26T09:13:19.943Z] [INFO]   15. add function: 'products'
[2025-05-26T09:13:19.944Z] [INFO]   16. add variable: 'products'
[2025-05-26T09:13:19.944Z] [INFO]   17. add variable: 'products_html'
[2025-05-26T09:13:19.944Z] [INFO]   18. add variable: 'product'
[2025-05-26T09:13:19.945Z] [INFO]   19. add variable: 'total_value'
[2025-05-26T09:13:19.945Z] [INFO]   20. add variable: 'content'
[2025-05-26T09:13:19.951Z] [INFO] ✅ SAVED documentation record: 820319815e9fde77b06d5b0d5f0d16b7 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\820319815e9fde77b06d5b0d5f0d16b7.json
[2025-05-26T09:13:19.956Z] [INFO] Successfully processed change: 820319815e9fde77b06d5b0d5f0d16b7
[2025-05-26T09:13:20.221Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- examples/03_templates.py
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:13:20.521Z] [INFO] Contextual analysis completed for: examples/03_templates.py
[2025-05-26T09:13:20.522Z] [INFO] Contextual analysis completed for examples/03_templates.py
[2025-05-26T09:13:20.522Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:20.522Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:20.523Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:20.523Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:20.524Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:20.524Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:20.524Z] [INFO] Generating AI documentation for: examples/03_templates.py
[2025-05-26T09:13:20.524Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:20.525Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:20.525Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:20.525Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:20.526Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:20.526Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:20.526Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:13:20.526Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:20.527Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:13:20.527Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:13:20.531Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:13:20.532Z] [WARN] Failed to generate AI documentation for: examples/03_templates.py
[2025-05-26T09:13:20.532Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 9e9cb9e37e2ac3d383f6910e3870c8fe
[2025-05-26T09:13:20.533Z] [INFO] 📄 File: examples/03_templates.py
[2025-05-26T09:13:20.533Z] [INFO] 🔄 Event type: modify
[2025-05-26T09:13:20.533Z] [INFO] 🔧 Semantic primitives: 20
[2025-05-26T09:13:20.533Z] [INFO]   1. add variable: 'app'
[2025-05-26T09:13:20.534Z] [INFO]   2. add variable: 'BASE_TEMPLATE'
[2025-05-26T09:13:20.534Z] [INFO]   3. add function: 'home'
[2025-05-26T09:13:20.534Z] [INFO]   4. add variable: 'current_time'
[2025-05-26T09:13:20.534Z] [INFO]   5. add variable: 'content'
[2025-05-26T09:13:20.535Z] [INFO]   6. add function: 'about'
[2025-05-26T09:13:20.535Z] [INFO]   7. add variable: 'app_info'
[2025-05-26T09:13:20.535Z] [INFO]   8. add variable: 'content'
[2025-05-26T09:13:20.536Z] [INFO]   9. add function: 'users'
[2025-05-26T09:13:20.536Z] [INFO]   10. add variable: 'users_list'
[2025-05-26T09:13:20.537Z] [INFO]   11. add variable: 'users_html'
[2025-05-26T09:13:20.537Z] [INFO]   12. add variable: 'user'
[2025-05-26T09:13:20.538Z] [INFO]   13. add variable: 'role_color'
[2025-05-26T09:13:20.538Z] [INFO]   14. add variable: 'content'
[2025-05-26T09:13:20.539Z] [INFO]   15. add function: 'products'
[2025-05-26T09:13:20.539Z] [INFO]   16. add variable: 'products'
[2025-05-26T09:13:20.540Z] [INFO]   17. add variable: 'products_html'
[2025-05-26T09:13:20.540Z] [INFO]   18. add variable: 'product'
[2025-05-26T09:13:20.540Z] [INFO]   19. add variable: 'total_value'
[2025-05-26T09:13:20.541Z] [INFO]   20. add variable: 'content'
[2025-05-26T09:13:20.547Z] [INFO] ✅ SAVED documentation record: 9e9cb9e37e2ac3d383f6910e3870c8fe to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\9e9cb9e37e2ac3d383f6910e3870c8fe.json
[2025-05-26T09:13:20.551Z] [INFO] Successfully processed change: 9e9cb9e37e2ac3d383f6910e3870c8fe
[2025-05-26T09:13:36.028Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='ignoredFilesPatterns'
[2025-05-26T09:13:36.028Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.029Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'ignoredFilesPatterns': {
  "key": "learningDocs.ignoredFilesPatterns",
  "defaultValue": [
    "**/node_modules/**",
    "**/.git/**"
  ]
}
[2025-05-26T09:13:36.030Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('ignoredFilesPatterns'): (2) ['**/node_modules/**', '**/.git/**'] (Type: object)
[2025-05-26T09:13:36.031Z] [INFO] File saved: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.032Z] [INFO] Processing change: modify - c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.032Z] [INFO] Getting previous version for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.186Z] [WARN] No previous version found for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.187Z] [INFO] Generated diff for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.187Z] [INFO] 🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.187Z] [INFO]   - Has semantic analyzer service: true
[2025-05-26T09:13:36.188Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:36.188Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:36.188Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.189Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:36.189Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:36.190Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:36.190Z] [INFO] [DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: true (Type: boolean)
[2025-05-26T09:13:36.190Z] [INFO]   - Semantic analysis enabled: true
[2025-05-26T09:13:36.190Z] [INFO]   - Event type: modify
[2025-05-26T09:13:36.191Z] [INFO]   - Has current content: true
[2025-05-26T09:13:36.191Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:36.191Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:36.191Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.192Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:36.192Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:36.192Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:36.193Z] [INFO] ✅ STARTING SEMANTIC ANALYSIS for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.193Z] [INFO] 📝 Detected language: plaintext
[2025-05-26T09:13:36.193Z] [INFO] 📂 File URI: file:///c%3A/Users/<USER>/Documents/augment-projects/learn-flask/c%3A/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/477831f6fb93dc0950381dbb5230838a/Augment.vscode-augment/Augment-Memories
[2025-05-26T09:13:36.194Z] [INFO] 🔍 SEMANTIC ANALYSIS START for c:\Users\<USER>\Documents\augment-projects\learn-flask\c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.194Z] [INFO] 📝 Language: plaintext
[2025-05-26T09:13:36.194Z] [INFO] 📄 Current content length: 79
[2025-05-26T09:13:36.194Z] [INFO] 📄 Previous content length: 0
[2025-05-26T09:13:36.195Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='semanticAnalysis' -> fullKey='enable.semanticAnalysis'
[2025-05-26T09:13:36.195Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.semanticAnalysis'
[2025-05-26T09:13:36.195Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.196Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.semanticAnalysis': {
  "key": "learningDocs.enable.semanticAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:36.196Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.semanticAnalysis'): true (Type: boolean)
[2025-05-26T09:13:36.197Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'semanticAnalysis': true (rawValue was: true)
[2025-05-26T09:13:36.197Z] [INFO] ⚙️ Semantic analysis enabled: true
[2025-05-26T09:13:36.197Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='semanticAnalysis.enabledLanguages'
[2025-05-26T09:13:36.198Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.198Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'semanticAnalysis.enabledLanguages': {
  "key": "learningDocs.semanticAnalysis.enabledLanguages",
  "defaultValue": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact",
    "python",
    "java",
    "csharp",
    "go",
    "ruby",
    "php"
  ]
}
[2025-05-26T09:13:36.199Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('semanticAnalysis.enabledLanguages'): (10) ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'] (Type: object)
[2025-05-26T09:13:36.199Z] [INFO] 🌐 Language plaintext supported: false
[2025-05-26T09:13:36.200Z] [WARN] ❌ Semantic analysis not enabled for language: plaintext
[2025-05-26T09:13:36.201Z] [WARN] ⚠️ SEMANTIC ANALYSIS RETURNED NULL for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.201Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='contextualAnalysis' -> fullKey='enable.contextualAnalysis'
[2025-05-26T09:13:36.201Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.contextualAnalysis'
[2025-05-26T09:13:36.202Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.202Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.contextualAnalysis': {
  "key": "learningDocs.enable.contextualAnalysis",
  "defaultValue": true
}
[2025-05-26T09:13:36.203Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.contextualAnalysis'): true (Type: boolean)
[2025-05-26T09:13:36.203Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'contextualAnalysis': true (rawValue was: true)
[2025-05-26T09:13:36.204Z] [INFO] Getting contextual info for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.314Z] [ERROR] Error finding related imports: Error: ENOENT: no such file or directory, open 'c:\Users\<USER>\Documents\augment-projects\learn-flask\c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories'
[2025-05-26T09:13:36.358Z] [WARN] Could not get git history: Error: Command failed: git log --pretty=format:%H|%an|%ad|%s --date=iso -- c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
fatal: not a git repository (or any of the parent directories): .git
[2025-05-26T09:13:36.626Z] [INFO] Contextual analysis completed for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.627Z] [INFO] Contextual analysis completed for c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.627Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:36.627Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:36.628Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.628Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:36.629Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:36.629Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:36.629Z] [INFO] Generating AI documentation for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.629Z] [INFO] [DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='aiDocumentation' -> fullKey='enable.aiDocumentation'
[2025-05-26T09:13:36.630Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='enable.aiDocumentation'
[2025-05-26T09:13:36.630Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.631Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'enable.aiDocumentation': {
  "key": "learningDocs.enable.aiDocumentation",
  "defaultValue": true
}
[2025-05-26T09:13:36.631Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('enable.aiDocumentation'): true (Type: boolean)
[2025-05-26T09:13:36.631Z] [INFO] [DEBUG_CONFIG] ✅ isFeatureEnabled result for 'aiDocumentation': true (rawValue was: true)
[2025-05-26T09:13:36.631Z] [INFO] [DEBUG_CONFIG] 🔍 Attempting to read config: Section='learningDocs', Key='fastAgentUrl'
[2025-05-26T09:13:36.632Z] [INFO] [DEBUG_CONFIG] 📋 All available keys in 'learningDocs' section: (8) ['has', 'get', 'update', 'inspect', 'fastAgentUrl', 'ignoredFilesPatterns', 'enable', 'semanticAnalysis']
[2025-05-26T09:13:36.632Z] [INFO] [DEBUG_CONFIG] 🔍 Inspect result for 'fastAgentUrl': {
  "key": "learningDocs.fastAgentUrl",
  "defaultValue": "http://localhost:8000/analyze"
}
[2025-05-26T09:13:36.633Z] [INFO] [DEBUG_CONFIG] 📄 Raw value from getConfiguration('learningDocs').get('fastAgentUrl'): http://localhost:8000/analyze (Type: string)
[2025-05-26T09:13:36.636Z] [ERROR] Error sending request to FastAgent: AggregateError
[2025-05-26T09:13:36.637Z] [WARN] Failed to generate AI documentation for: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.637Z] [INFO] 💾 SAVING DOCUMENTATION RECORD: 542ceb1f00fc1b388bce776eeb216b92
[2025-05-26T09:13:36.638Z] [INFO] 📄 File: c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\477831f6fb93dc0950381dbb5230838a\Augment.vscode-augment\Augment-Memories
[2025-05-26T09:13:36.638Z] [INFO] 🔄 Event type: modify
[2025-05-26T09:13:36.639Z] [INFO] 🔧 Semantic primitives: 0
[2025-05-26T09:13:36.642Z] [INFO] ✅ SAVED documentation record: 542ceb1f00fc1b388bce776eeb216b92 to c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\learning-docs-publisher.learning-docs\documentation\learn-flask\542ceb1f00fc1b388bce776eeb216b92.json
[2025-05-26T09:13:36.645Z] [INFO] Successfully processed change: 542ceb1f00fc1b388bce776eeb216b92
