# Learning Docs

A VSCode extension that detects code changes and provides AI-powered learning documentation.

## Features

Learning Docs is a powerful VSCode extension that helps you understand code changes in your project by:

- **Detecting Code Changes**: Automatically detects when you create, modify, or delete files
- **Generating Diffs**: Creates detailed diffs between versions of your code
- **Semantic Analysis**: Analyzes code at a structural level to identify functions, classes, and methods that were added, removed, or modified
- **Contextual Analysis**: Gathers contextual information about changes, including related files, references, imports, and dependencies
- **AI-Powered Documentation**: Generates natural language explanations of what the changes do and why they matter
- **Comprehensive UI**: Provides an intuitive interface for exploring documentation and change history

### Change Detection

Learning Docs monitors your workspace for file changes and automatically processes them when they occur. It handles:

- File creation
- File modification
- File deletion
- External changes (e.g., from git operations)

### Semantic Analysis

The extension analyzes code at a structural level to identify:

- Added, removed, or modified functions
- Added, removed, or modified classes and methods
- Added, removed, or modified properties and variables
- Changes to imports and exports

### Contextual Analysis

Learning Docs gathers contextual information about changes, including:

- Related files that might be affected by the change
- References to the changed elements in other files
- Related imports and dependencies
- Commit history for the file
- Related test files

### AI-Powered Documentation

The extension generates AI-powered documentation for changes, including:

- A summary of what the change does
- A detailed explanation of the change
- The potential impact of the change on the codebase
- Suggestions for improvements or potential issues
- Learning resources related to the concepts in the change
- Code examples that demonstrate the concepts

## Installation

### From VS Code Marketplace

1. Open VS Code
2. Go to the Extensions view (Ctrl+Shift+X)
3. Search for "Learning Docs"
4. Click Install

### From VSIX File

1. Download the latest `.vsix` file from the [releases page](https://github.com/ALPHAbilal/one_one/releases)
2. Open VS Code
3. Go to the Extensions view (Ctrl+Shift+X)
4. Click the "..." menu in the top-right corner
5. Select "Install from VSIX..."
6. Choose the downloaded file

## Usage

### Viewing Documentation

There are several ways to access documentation:

1. **Status Bar**: Click the "Learning Docs" item in the status bar
2. **Explorer View**: Open the "Learning Documentation" view in the Explorer sidebar
3. **Command Palette**: Press Ctrl+Shift+P and search for "Learning Docs: Show Documentation Panel"
4. **Context Menu**: Right-click on a file in the editor and select "Learning Docs: Show Documentation"

### Documentation Panel

The documentation panel shows detailed information about changes, including:

- AI analysis with summary and explanation
- Semantic changes (added, removed, or modified elements)
- Diff between versions
- Contextual information (related files, references, etc.)

### Documentation Explorer

The Documentation Explorer view in the sidebar shows a tree of files and changes:

- Files are shown at the root level
- Changes for each file are shown as children
- Click on a change to view its documentation

## Configuration

Learning Docs can be configured through VS Code settings:

### General Settings

- `learningDocs.enable`: Enable/disable the Learning Docs extension (default: `true`)

### Feature Settings

- `learningDocs.enable.semanticAnalysis`: Enable/disable semantic analysis of code changes (default: `true`)
- `learningDocs.enable.contextualAnalysis`: Enable/disable contextual analysis of code changes (default: `true`)
- `learningDocs.enable.aiDocumentation`: Enable/disable AI-powered documentation generation (default: `true`)

### File Settings

- `learningDocs.ignoredFilesPatterns`: Patterns for files to ignore (default: `["**/node_modules/**", "**/.git/**"]`)
- `learningDocs.semanticAnalysis.enabledExtensions`: File extensions for which semantic analysis is enabled (default: `[".ts", ".js", ".tsx", ".jsx", ".py", ".java", ".cs", ".go", ".rb", ".php"]`)

### AI Settings

- `learningDocs.fastAgentUrl`: URL for the FastAgent backend (default: `"http://localhost:8000/analyze"`)

## Requirements

- VS Code 1.60.0 or higher
- Node.js 14.0.0 or higher (for development)

## Known Issues

- The FastAgent backend is required for full AI documentation generation. Without it, a fallback mode with limited functionality is used.
- Semantic analysis may not work perfectly for all programming languages and complex code structures.

## Release Notes

### 0.0.1

- Initial release
- Core infrastructure and change detection
- File history and diff generation
- Semantic analysis
- Contextual analysis
- AI documentation
- User interface
