// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// Import services
import { LoggerService } from './services/logger-service';
import { ConfigService } from './services/config-service';
import { WorkspaceService } from './services/workspace-service';
import { StorageService } from './services/storage-service';
import { FileHistoryService } from './services/file-history-service';
import { DiffService } from './services/diff-service';
import { SemanticAnalyzerService } from './services/semantic-analyzer-service';
import { ContextAnalyzerService } from './services/context-analyzer-service';
import { AIDocumentationService } from './services/ai-documentation-service';
import { ChangeEventAggregatorService } from './services/change-event-aggregator-service';
import { ChangeProcessorService } from './services/change-processor-service';

// Import UI components
import { DocumentationPanel } from './ui/documentation-panel';
import { DocumentationTreeProvider } from './ui/documentation-tree-provider';
import { StatusBarItem } from './ui/status-bar-item';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {
	// Initialize services
	const loggerService = new LoggerService();
	const configService = new ConfigService();
	const workspaceService = new WorkspaceService(configService, loggerService);
	const storageService = new StorageService(context, loggerService, workspaceService);
	const fileHistoryService = new FileHistoryService(context, loggerService, workspaceService);
	const diffService = new DiffService(loggerService);
	const semanticAnalyzerService = new SemanticAnalyzerService(loggerService, configService, workspaceService);
	const contextAnalyzerService = new ContextAnalyzerService(loggerService, configService, workspaceService);
	const aiDocumentationService = new AIDocumentationService(loggerService, configService);
	const changeEventAggregator = new ChangeEventAggregatorService(loggerService, workspaceService);
	const changeProcessor = new ChangeProcessorService(
		loggerService,
		changeEventAggregator,
		storageService,
		fileHistoryService,
		diffService,
		semanticAnalyzerService,
		contextAnalyzerService,
		aiDocumentationService,
		configService
	);

	// Start the change processor
	changeProcessor.start();

	// Initialize UI components
	const treeDataProvider = new DocumentationTreeProvider(loggerService, storageService);
	const statusBarItem = new StatusBarItem(loggerService);

	// Register tree view
	const treeView = vscode.window.createTreeView('learningDocsExplorer', {
		treeDataProvider
	});

	// Register commands
	context.subscriptions.push(
		vscode.commands.registerCommand('learningDocs.showDocumentationPanel', () => {
			DocumentationPanel.createOrShow(context.extensionUri, loggerService, storageService);
		}),

		vscode.commands.registerCommand('learningDocs.showDocumentation', async (recordId?: string) => {
			const panel = DocumentationPanel.createOrShow(context.extensionUri, loggerService, storageService);

			if (recordId) {
				// Show documentation for a specific record
				await panel.showDocumentationForRecord(recordId);
			} else if (vscode.window.activeTextEditor) {
				// Show documentation for the active file
				const filePath = workspaceService.getRelativePath(vscode.window.activeTextEditor.document.uri);
				if (filePath) {
					await panel.showDocumentationForFile(filePath);
				}
			}
		}),

		vscode.commands.registerCommand('learningDocs.refreshDocumentation', () => {
			treeDataProvider.refresh();
		})
	);

	// Add services and UI components to disposables
	context.subscriptions.push(
		treeView,
		statusBarItem,
		{ dispose: () => changeProcessor.dispose() },
		{ dispose: () => changeEventAggregator.dispose() },
		{ dispose: () => loggerService.dispose() }
	);

	loggerService.info('Learning Docs extension activated');
}

// This method is called when your extension is deactivated
export function deactivate() {
	console.log('Learning Docs extension deactivated');
}
