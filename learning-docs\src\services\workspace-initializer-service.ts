import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import { IWorkspaceInitializerService } from '../interfaces/workspace-initializer';
import { ILoggerService } from '../interfaces/logger';
import { IWorkspaceService } from '../interfaces/workspace';
import { IStorageService } from '../interfaces/storage-service';
import { IFileHistoryService } from '../interfaces/file-history';
import { IDiffService } from '../interfaces/diff';
import { ChangeDescriptor } from '../interfaces/change';
import { ChangeDocumentationRecord } from '../interfaces/storage';

/**
 * Implementation of the workspace initializer service.
 */
export class WorkspaceInitializerService implements IWorkspaceInitializerService {
    private readonly INITIALIZATION_MARKER_FILE = '.learning-docs-initialized';
    
    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly loggerService: ILoggerService,
        private readonly workspaceService: IWorkspaceService,
        private readonly storageService: IStorageService,
        private readonly fileHistoryService: IFileHistoryService,
        private readonly diffService: IDiffService
    ) {}
    
    /**
     * Initialize the workspace on extension activation.
     */
    public async initializeWorkspace(): Promise<void> {
        this.loggerService.info('Starting workspace initialization');
        
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            this.loggerService.warn('No workspace found, skipping initialization');
            return;
        }
        
        const isFirstRun = await this.isFirstRunInWorkspace();
        
        if (isFirstRun) {
            this.loggerService.info('First run detected, performing initial workspace scan');
            await this.performInitialWorkspaceScan();
            await this.markWorkspaceAsInitialized();
        } else {
            this.loggerService.info('Resuming workspace tracking (existing data found)');
        }
        
        this.loggerService.info('Workspace initialization completed');
    }
    
    /**
     * Check if this is the first run in the current workspace.
     */
    public async isFirstRunInWorkspace(): Promise<boolean> {
        try {
            // Check for initialization marker in workspace state
            const isInitialized = this.context.workspaceState.get<boolean>('learningDocs.initialized', false);
            if (isInitialized) {
                return false;
            }
            
            // Check for existing documentation records
            const hasRecords = await this.storageService.hasExistingRecords();
            if (hasRecords) {
                // Records exist but marker is missing, set the marker and consider it not first run
                await this.markWorkspaceAsInitialized();
                return false;
            }
            
            // Check for existing file history cache
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (workspaceRoot) {
                const cacheDir = path.join(
                    this.context.globalStorageUri.fsPath, 
                    '.learningDocsCache', 
                    path.basename(workspaceRoot.fsPath)
                );
                
                if (fs.existsSync(cacheDir)) {
                    const cacheFiles = await fs.promises.readdir(cacheDir);
                    if (cacheFiles.length > 0) {
                        // Cache exists but marker is missing, set the marker and consider it not first run
                        await this.markWorkspaceAsInitialized();
                        return false;
                    }
                }
            }
            
            return true;
        } catch (error) {
            this.loggerService.error(`Error checking first run status: ${error}`);
            // Default to first run to be safe
            return true;
        }
    }
    
    /**
     * Perform an initial workspace scan to baseline all existing files.
     */
    public async performInitialWorkspaceScan(): Promise<void> {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            this.loggerService.warn('No workspace root found for initial scan');
            return;
        }
        
        this.loggerService.info('Starting initial workspace scan');
        
        try {
            // Get ignored patterns for the file search
            const ignoredPatterns = this.getIgnoredPatterns();
            
            // Find all files in the workspace
            const files = await vscode.workspace.findFiles('**/*', `{${ignoredPatterns.join(',')}}`);
            
            this.loggerService.info(`Found ${files.length} files to baseline`);
            
            // Process files in batches to avoid blocking the UI
            const batchSize = 10;
            for (let i = 0; i < files.length; i += batchSize) {
                const batch = files.slice(i, i + batchSize);
                await this.processBatch(batch);
                
                // Yield to the event loop
                await new Promise(resolve => setImmediate(resolve));
            }
            
            this.loggerService.info(`Initial workspace scan completed: ${files.length} files baselined`);
        } catch (error) {
            this.loggerService.error(`Error during initial workspace scan: ${error}`);
            throw error;
        }
    }
    
    /**
     * Process a batch of files during the initial scan.
     */
    private async processBatch(files: vscode.Uri[]): Promise<void> {
        for (const fileUri of files) {
            try {
                await this.baselineFile(fileUri);
            } catch (error) {
                this.loggerService.error(`Failed to baseline file ${fileUri.fsPath}: ${error}`);
                // Continue with other files
            }
        }
    }
    
    /**
     * Baseline a single file.
     */
    private async baselineFile(fileUri: vscode.Uri): Promise<void> {
        const relativePath = this.workspaceService.getRelativePath(fileUri);
        if (!relativePath) {
            return;
        }
        
        // Read the file content
        const document = await vscode.workspace.openTextDocument(fileUri);
        const content = document.getText();
        
        // Store in file history cache as baseline
        await this.fileHistoryService.baselineFile(relativePath, content);
        
        // Create a baseline documentation record
        const record: ChangeDocumentationRecord = {
            id: this.generateId(relativePath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'baseline',
            currentContent: content,
            previousContent: undefined,
            rawDiff: await this.diffService.generateUnifiedDiff('', content, relativePath, relativePath)
        };
        
        // Save the baseline record
        await this.storageService.saveDocumentationRecord(record);
        
        this.loggerService.info(`Baselined file: ${relativePath}`);
    }
    
    /**
     * Mark the workspace as successfully initialized.
     */
    public async markWorkspaceAsInitialized(): Promise<void> {
        try {
            await this.context.workspaceState.update('learningDocs.initialized', true);
            this.loggerService.info('Workspace marked as initialized');
        } catch (error) {
            this.loggerService.error(`Failed to mark workspace as initialized: ${error}`);
        }
    }
    
    /**
     * Get ignored patterns for file search.
     */
    private getIgnoredPatterns(): string[] {
        // Use the same patterns as the workspace service
        return [
            '**/node_modules/**',
            '**/.git/**',
            '**/out/**',
            '**/dist/**',
            '**/.vscode/**',
            '**/*.log'
        ];
    }
    
    /**
     * Generate a unique ID for a baseline record.
     */
    private generateId(filePath: string): string {
        const timestamp = Date.now().toString();
        const hash = crypto.createHash('md5').update(`baseline:${filePath}:${timestamp}`).digest('hex');
        return hash;
    }
}
