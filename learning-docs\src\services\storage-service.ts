import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { IStorageService } from '../interfaces/storage-service';
import { ChangeDocumentationRecord } from '../interfaces/storage';
import { ILoggerService } from '../interfaces/logger';
import { IWorkspaceService } from '../interfaces/workspace';

/**
 * Implementation of the storage service.
 */
export class StorageService implements IStorageService {
    private storageDir: string;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly loggerService: ILoggerService,
        private readonly workspaceService: IWorkspaceService
    ) {
        // Create storage directory within the extension's global storage
        this.storageDir = path.join(context.globalStorageUri.fsPath, 'documentation');
        this.ensureStorageDirectoryExists();
    }

    /**
     * Save a documentation record.
     * @param record The record to save.
     */
    public async saveDocumentationRecord(record: ChangeDocumentationRecord): Promise<void> {
        try {
            this.loggerService.info(`💾 SAVING DOCUMENTATION RECORD: ${record.id}`);
            this.loggerService.info(`📄 File: ${record.filePath}`);
            this.loggerService.info(`🔄 Event type: ${record.eventType}`);
            this.loggerService.info(`🔧 Semantic primitives: ${record.semanticPrimitives?.length || 0}`);

            if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
                record.semanticPrimitives.forEach((primitive, index) => {
                    this.loggerService.info(`  ${index + 1}. ${primitive.operation} ${primitive.elementType}: '${primitive.elementName}'`);
                });
            }

            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);

            // Ensure workspace directory exists
            if (!fs.existsSync(workspaceDir)) {
                fs.mkdirSync(workspaceDir, { recursive: true });
            }

            // Create a file path for the record
            const recordPath = path.join(workspaceDir, `${record.id}.json`);

            // Write the record to the file
            await fs.promises.writeFile(recordPath, JSON.stringify(record, null, 2));

            this.loggerService.info(`✅ SAVED documentation record: ${record.id} to ${recordPath}`);
        } catch (error) {
            this.loggerService.error(`💥 Failed to save documentation record: ${error}`);
            throw error;
        }
    }

    /**
     * Get a record by its ID.
     * @param id The ID of the record to retrieve.
     * @returns The record, or undefined if not found.
     */
    public async getRecordById(id: string): Promise<ChangeDocumentationRecord | undefined> {
        try {
            const workspaceId = this.getWorkspaceId();
            const recordPath = path.join(this.storageDir, workspaceId, `${id}.json`);

            if (!fs.existsSync(recordPath)) {
                return undefined;
            }

            const content = await fs.promises.readFile(recordPath, 'utf-8');
            return JSON.parse(content) as ChangeDocumentationRecord;
        } catch (error) {
            this.loggerService.error(`Failed to get record by ID: ${error}`);
            return undefined;
        }
    }

    /**
     * Get all records for a specific file.
     * @param filePath The path to the file.
     * @returns An array of records for the file.
     */
    public async getRecordsByFile(filePath: string): Promise<ChangeDocumentationRecord[]> {
        try {
            this.loggerService.info(`📂 LOADING RECORDS for file: ${filePath}`);

            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);

            if (!fs.existsSync(workspaceDir)) {
                this.loggerService.info(`⚠️ Workspace directory does not exist: ${workspaceDir}`);
                return [];
            }

            const files = await fs.promises.readdir(workspaceDir);
            const records: ChangeDocumentationRecord[] = [];
            this.loggerService.info(`📁 Found ${files.length} files in workspace directory`);

            for (const file of files) {
                if (path.extname(file) === '.json') {
                    const recordPath = path.join(workspaceDir, file);
                    const content = await fs.promises.readFile(recordPath, 'utf-8');
                    const record = JSON.parse(content) as ChangeDocumentationRecord;

                    if (record.filePath === filePath) {
                        this.loggerService.info(`📄 Found matching record: ${record.id}`);
                        this.loggerService.info(`  - Event type: ${record.eventType}`);
                        this.loggerService.info(`  - Semantic primitives: ${record.semanticPrimitives?.length || 0}`);

                        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
                            record.semanticPrimitives.forEach((primitive, index) => {
                                this.loggerService.info(`    ${index + 1}. ${primitive.operation} ${primitive.elementType}: '${primitive.elementName}'`);
                            });
                        }

                        records.push(record);
                    }
                }
            }

            // Sort by timestamp (newest first)
            const sortedRecords = records.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
            this.loggerService.info(`✅ LOADED ${sortedRecords.length} records for ${filePath}`);

            return sortedRecords;
        } catch (error) {
            this.loggerService.error(`💥 Failed to get records by file: ${error}`);
            return [];
        }
    }

    /**
     * Get all file paths that have documentation records.
     * @returns An array of file paths.
     */
    public async getAllFilePaths(): Promise<string[]> {
        try {
            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);

            if (!fs.existsSync(workspaceDir)) {
                return [];
            }

            const files = await fs.promises.readdir(workspaceDir);
            const filePaths = new Set<string>();

            for (const file of files) {
                if (path.extname(file) === '.json') {
                    const recordPath = path.join(workspaceDir, file);
                    const content = await fs.promises.readFile(recordPath, 'utf-8');
                    const record = JSON.parse(content) as ChangeDocumentationRecord;

                    if (record.filePath) {
                        filePaths.add(record.filePath);
                    }
                }
            }

            return Array.from(filePaths).sort();
        } catch (error) {
            this.loggerService.error(`Failed to get all file paths: ${error}`);
            return [];
        }
    }

    /**
     * Check if the workspace has any existing documentation records.
     * @returns A promise that resolves to true if records exist, false otherwise.
     */
    public async hasExistingRecords(): Promise<boolean> {
        try {
            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);

            if (!fs.existsSync(workspaceDir)) {
                return false;
            }

            const files = await fs.promises.readdir(workspaceDir);

            // Check if there are any JSON files (documentation records)
            return files.some(file => path.extname(file) === '.json');
        } catch (error) {
            this.loggerService.error(`Failed to check for existing records: ${error}`);
            return false;
        }
    }

    /**
     * Ensure the storage directory exists.
     */
    private ensureStorageDirectoryExists(): void {
        if (!fs.existsSync(this.storageDir)) {
            fs.mkdirSync(this.storageDir, { recursive: true });
        }
    }

    /**
     * Get a unique identifier for the current workspace.
     * @returns A string identifier for the workspace.
     */
    private getWorkspaceId(): string {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return 'no-workspace';
        }

        // Use the last segment of the workspace path as an identifier
        return path.basename(workspaceRoot.fsPath);
    }
}
