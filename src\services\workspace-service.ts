import * as vscode from 'vscode';
import * as path from 'path';
import { IWorkspaceService } from '../interfaces/workspace';
import { IConfigService } from '../interfaces/config';
import { ILoggerService } from '../interfaces/logger';
import { minimatch } from 'minimatch';

/**
 * Implementation of the workspace service.
 */
export class WorkspaceService implements IWorkspaceService {
    constructor(
        private readonly configService: IConfigService,
        private readonly loggerService: ILoggerService
    ) {}
    
    /**
     * Get the root URI of the workspace.
     * @returns The workspace root URI, or undefined if not in a workspace.
     */
    public getWorkspaceRoot(): vscode.Uri | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            this.loggerService.warn('No workspace folder found');
            return undefined;
        }
        
        // For simplicity, use the first workspace folder
        return workspaceFolders[0].uri;
    }
    
    /**
     * Get the path relative to the workspace root.
     * @param uri The URI to convert to a relative path.
     * @returns The relative path, or undefined if not in a workspace.
     */
    public getRelativePath(uri: vscode.Uri): string | undefined {
        const workspaceRoot = this.getWorkspaceRoot();
        if (!workspaceRoot) {
            return undefined;
        }
        
        return vscode.workspace.asRelativePath(uri, false);
    }
    
    /**
     * Check if a path should be ignored based on configuration.
     * @param filePath The path to check.
     * @returns True if the path should be ignored, false otherwise.
     */
    public isPathIgnored(filePath: string): boolean {
        const ignoredPatterns = this.configService.getSetting<string[]>('ignoredFilesPatterns') || [
            '**/node_modules/**',
            '**/.git/**'
        ];
        
        return ignoredPatterns.some(pattern => minimatch(filePath, pattern));
    }
}
