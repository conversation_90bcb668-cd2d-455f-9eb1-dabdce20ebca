import * as vscode from 'vscode';
import * as path from 'path';
import { ISemanticAnalyzerService, SemanticAnalysisResult, SemanticPrimitive } from '../interfaces/semantic-analyzer';
import { SemanticAnalysisConfig } from '../interfaces/semantic';
import { ILoggerService } from '../interfaces/logger';
import { IConfigService } from '../interfaces/config';
import { IWorkspaceService } from '../interfaces/workspace';

/**
 * Implementation of the semantic analyzer service.
 */
export class SemanticAnalyzerService implements ISemanticAnalyzerService {
    constructor(
        private readonly loggerService: ILoggerService,
        private readonly configService: IConfigService,
        private readonly workspaceService: IWorkspaceService
    ) {}

    /**
     * Get semantic analysis for a change.
     *
     * @param previousContent The previous content of the file (can be undefined for new files).
     * @param currentContent The current content of the file.
     * @param uri The URI of the file for context and LSP calls.
     * @param languageId The language ID of the file.
     * @returns A promise that resolves to the semantic analysis result, or null if analysis is not possible.
     */
    public async getSemanticAnalysis(
        previousContent: string | undefined,
        currentContent: string,
        uri: vscode.Uri,
        languageId: string
    ): Promise<SemanticAnalysisResult | null> {
        this.loggerService.info(`🔍 SEMANTIC ANALYSIS START for ${uri.fsPath}`);
        this.loggerService.info(`📝 Language: ${languageId}`);
        this.loggerService.info(`📄 Current content length: ${currentContent.length}`);
        this.loggerService.info(`📄 Previous content length: ${previousContent?.length || 0}`);

        try {
            // Check if semantic analysis is enabled
            const isEnabled = this.configService.isFeatureEnabled('semanticAnalysis');
            this.loggerService.info(`⚙️ Semantic analysis enabled: ${isEnabled}`);
            if (!isEnabled) {
                this.loggerService.warn('❌ Semantic analysis is disabled in configuration');
                return null;
            }

            // Check if language is supported
            const isSupported = this.isLanguageSupported(languageId);
            this.loggerService.info(`🌐 Language ${languageId} supported: ${isSupported}`);
            if (!isSupported) {
                this.loggerService.warn(`❌ Semantic analysis not enabled for language: ${languageId}`);
                return null;
            }

            // Check text similarity threshold
            if (previousContent && this.shouldSkipDueToSimilarity(previousContent, currentContent)) {
                this.loggerService.warn('⏭️ Skipping semantic analysis due to high text similarity');
                return null;
            }

            this.loggerService.info(`🚀 Starting semantic analysis for ${languageId} file: ${uri.fsPath}`);

            // Get document symbols for current content
            this.loggerService.info('📊 Getting current document symbols...');
            const currentSymbols = await this.getDocumentSymbols(currentContent, languageId, uri);
            if (!currentSymbols) {
                this.loggerService.error('❌ Failed to get current document symbols');
                return null;
            }
            this.loggerService.info(`✅ Found ${currentSymbols.length} current symbols`);

            // Get document symbols for previous content (if exists)
            let previousSymbols: vscode.DocumentSymbol[] | undefined;
            if (previousContent) {
                this.loggerService.info('📊 Getting previous document symbols...');
                previousSymbols = await this.getDocumentSymbols(previousContent, languageId);
                if (previousSymbols) {
                    this.loggerService.info(`✅ Found ${previousSymbols.length} previous symbols`);
                } else {
                    this.loggerService.warn('⚠️ Failed to get previous document symbols');
                }
            } else {
                this.loggerService.info('📄 No previous content - treating as new file');
            }

            // Diff the symbol trees
            this.loggerService.info('🔄 Diffing symbol trees...');
            const primitives = this.diffSymbolTrees(previousSymbols || [], currentSymbols);
            this.loggerService.info(`🎯 Generated ${primitives.length} semantic primitives`);

            // Log each primitive for debugging
            primitives.forEach((primitive, index) => {
                this.loggerService.info(`  ${index + 1}. ${primitive.operation.toUpperCase()} ${primitive.elementType}: '${primitive.elementName}'${primitive.signature ? ` (${primitive.signature})` : ''}`);
            });

            // Calculate confidence and summary
            const confidence = this.calculateConfidence(primitives, currentSymbols);
            const summary = this.calculateSummary(primitives);

            this.loggerService.info(`📈 Analysis confidence: ${confidence}`);
            this.loggerService.info(`📊 Summary: +${summary.added} -${summary.removed} ~${summary.modified} ↔${summary.renamed}`);
            this.loggerService.info(`✅ SEMANTIC ANALYSIS COMPLETED: ${primitives.length} primitives found`);

            return {
                primitives,
                languageId,
                confidence,
                summary
            };
        } catch (error) {
            this.loggerService.error(`💥 ERROR during semantic analysis: ${error}`);
            this.loggerService.error(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace'}`);
            return null;
        }
    }

    /**
     * Check if semantic analysis is enabled for a language.
     * @param languageId The language ID.
     * @returns True if semantic analysis is enabled, false otherwise.
     */
    private isLanguageSupported(languageId: string): boolean {
        const enabledLanguages = this.configService.getSetting<string[]>('semanticAnalysis.enabledLanguages') || [
            'typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'
        ];

        return enabledLanguages.includes(languageId);
    }

    /**
     * Check if semantic analysis should be skipped due to high text similarity.
     * @param previousContent The previous content.
     * @param currentContent The current content.
     * @returns True if analysis should be skipped, false otherwise.
     */
    private shouldSkipDueToSimilarity(previousContent: string, currentContent: string): boolean {
        const threshold = this.configService.getSetting<number>('semanticAnalysis.skipIfTextSimilarityAbove') || 0.98;

        // Simple similarity calculation based on character differences
        const maxLength = Math.max(previousContent.length, currentContent.length);
        if (maxLength === 0) {
            return true;
        }

        const similarity = 1 - (Math.abs(previousContent.length - currentContent.length) / maxLength);
        return similarity > threshold;
    }

    /**
     * Get the language ID from a file extension.
     * @param extension The file extension.
     * @returns The language ID, or undefined if not found.
     */
    private getLanguageIdFromExtension(extension: string): string | undefined {
        const extensionToLanguageMap: Record<string, string> = {
            '.ts': 'typescript',
            '.js': 'javascript',
            '.tsx': 'typescriptreact',
            '.jsx': 'javascriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.md': 'markdown'
        };

        return extensionToLanguageMap[extension];
    }

    /**
     * Get document symbols for file content.
     * @param content The file content.
     * @param languageId The language ID.
     * @param tempUri Optional URI for context (used for current content).
     * @returns A promise that resolves to an array of document symbols, or undefined if failed.
     */
    private async getDocumentSymbols(
        content: string,
        languageId: string,
        tempUri?: vscode.Uri
    ): Promise<vscode.DocumentSymbol[] | undefined> {
        this.loggerService.info(`🔍 Getting document symbols for ${languageId} (${content.length} chars)`);

        try {
            // For current content, use the provided URI if available
            if (tempUri) {
                this.loggerService.info(`📂 Checking for existing open document: ${tempUri.toString()}`);
                const openDoc = vscode.workspace.textDocuments.find(doc => doc.uri.toString() === tempUri.toString());
                if (openDoc && openDoc.getText() === content) {
                    this.loggerService.info('✅ Found matching open document, using it for symbol extraction');
                    const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                        'vscode.executeDocumentSymbolProvider',
                        openDoc.uri
                    );
                    this.loggerService.info(`📊 LSP returned ${symbols?.length || 0} symbols from open document`);
                    return symbols || [];
                } else {
                    this.loggerService.info('📄 Open document not found or content mismatch, creating temporary document');
                }
            }

            // Create a temporary document with the content
            this.loggerService.info(`📝 Creating temporary document with language: ${languageId}`);
            const document = await vscode.workspace.openTextDocument({
                language: languageId,
                content: content
            });
            this.loggerService.info(`✅ Temporary document created: ${document.uri.toString()}`);

            // Get document symbols using the language server
            this.loggerService.info('🔍 Executing vscode.executeDocumentSymbolProvider...');
            const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                'vscode.executeDocumentSymbolProvider',
                document.uri
            );

            this.loggerService.info(`📊 LSP returned ${symbols?.length || 0} symbols`);

            // Log symbol details for debugging
            if (symbols && symbols.length > 0) {
                symbols.forEach((symbol, index) => {
                    this.loggerService.info(`  Symbol ${index + 1}: ${symbol.kind} '${symbol.name}' ${symbol.detail || ''}`);
                    if (symbol.children && symbol.children.length > 0) {
                        symbol.children.forEach((child, childIndex) => {
                            this.loggerService.info(`    Child ${childIndex + 1}: ${child.kind} '${child.name}' ${child.detail || ''}`);
                        });
                    }
                });
            } else {
                this.loggerService.warn('⚠️ No symbols returned from LSP');
            }

            return symbols || [];
        } catch (error) {
            this.loggerService.error(`💥 Error getting document symbols: ${error}`);
            this.loggerService.error(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace'}`);
            return undefined;
        }
    }

    /**
     * Diff two symbol trees to find semantic changes.
     * @param previousSymbols The previous symbols.
     * @param currentSymbols The current symbols.
     * @returns An array of semantic primitives representing the changes.
     */
    private diffSymbolTrees(
        previousSymbols: vscode.DocumentSymbol[],
        currentSymbols: vscode.DocumentSymbol[]
    ): SemanticPrimitive[] {
        this.loggerService.info(`[DIFF_SYMBOLS] 🔄 Starting symbol tree diffing`);
        this.loggerService.info(`[DIFF_SYMBOLS] 📊 Old symbols count: ${previousSymbols?.length || 0}, New symbols count: ${currentSymbols?.length || 0}`);

        // Log symbol details for debugging
        this.loggerService.info(`[DIFF_SYMBOLS] 📋 OLD SYMBOLS:`);
        previousSymbols?.forEach((s, index) => {
            this.loggerService.info(`  ${index + 1}. ${s.name} (Kind: ${s.kind}, Detail: '${s.detail || 'none'}')`);
            if (s.children && s.children.length > 0) {
                s.children.forEach((child, childIndex) => {
                    this.loggerService.info(`    ${childIndex + 1}. ${child.name} (Kind: ${child.kind}, Detail: '${child.detail || 'none'}')`);
                });
            }
        });

        this.loggerService.info(`[DIFF_SYMBOLS] 📋 NEW SYMBOLS:`);
        currentSymbols?.forEach((s, index) => {
            this.loggerService.info(`  ${index + 1}. ${s.name} (Kind: ${s.kind}, Detail: '${s.detail || 'none'}')`);
            if (s.children && s.children.length > 0) {
                s.children.forEach((child, childIndex) => {
                    this.loggerService.info(`    ${childIndex + 1}. ${child.name} (Kind: ${child.kind}, Detail: '${child.detail || 'none'}')`);
                });
            }
        });

        const primitives: SemanticPrimitive[] = [];
        const maxDepth = this.configService.getSetting<number>('semanticAnalysis.maxNestingDepth') || 3;
        this.loggerService.info(`[DIFF_SYMBOLS] 🔧 Max nesting depth: ${maxDepth}`);

        // Create maps for quick lookup
        const previousSymbolMap = new Map<string, vscode.DocumentSymbol>();
        const currentSymbolMap = new Map<string, vscode.DocumentSymbol>();

        // Flatten symbol trees and create maps
        this.loggerService.info(`[DIFF_SYMBOLS] 🔧 Flattening symbol trees...`);
        this.flattenSymbolTree(previousSymbols, previousSymbolMap, '', 0, maxDepth);
        this.flattenSymbolTree(currentSymbols, currentSymbolMap, '', 0, maxDepth);

        this.loggerService.info(`[DIFF_SYMBOLS] 📊 Flattened - Previous: ${previousSymbolMap.size} symbols, Current: ${currentSymbolMap.size} symbols`);

        // Log flattened keys for debugging
        this.loggerService.info(`[DIFF_SYMBOLS] 🔑 Previous keys: [${Array.from(previousSymbolMap.keys()).join(', ')}]`);
        this.loggerService.info(`[DIFF_SYMBOLS] 🔑 Current keys: [${Array.from(currentSymbolMap.keys()).join(', ')}]`);

        // Find added symbols
        this.loggerService.info(`[DIFF_SYMBOLS] ➕ Looking for ADDED symbols...`);
        for (const [key, symbol] of currentSymbolMap.entries()) {
            if (!previousSymbolMap.has(key)) {
                this.loggerService.info(`[DIFF_SYMBOLS] ✅ FOUND ADD: '${key}' -> ${symbol.name} (Kind: ${symbol.kind})`);
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'add', key));
            }
        }

        // Find removed symbols
        this.loggerService.info(`[DIFF_SYMBOLS] ➖ Looking for REMOVED symbols...`);
        for (const [key, symbol] of previousSymbolMap.entries()) {
            if (!currentSymbolMap.has(key)) {
                this.loggerService.info(`[DIFF_SYMBOLS] ✅ FOUND REMOVE: '${key}' -> ${symbol.name} (Kind: ${symbol.kind})`);
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'remove', key));
            }
        }

        // Find modified symbols
        this.loggerService.info(`[DIFF_SYMBOLS] ✏️ Looking for MODIFIED symbols...`);
        for (const [key, currentSymbol] of currentSymbolMap.entries()) {
            const previousSymbol = previousSymbolMap.get(key);
            if (previousSymbol) {
                this.loggerService.info(`[DIFF_SYMBOLS] 🔍 Comparing '${key}': '${previousSymbol.detail || 'none'}' vs '${currentSymbol.detail || 'none'}'`);
                if (this.hasSymbolChanged(previousSymbol, currentSymbol)) {
                    this.loggerService.info(`[DIFF_SYMBOLS] ✅ FOUND MODIFY: '${key}' -> Detail changed from '${previousSymbol.detail || 'none'}' to '${currentSymbol.detail || 'none'}'`);
                    primitives.push(this.createModifiedPrimitive(previousSymbol, currentSymbol, key));
                } else {
                    this.loggerService.info(`[DIFF_SYMBOLS] ⏭️ No change detected for '${key}'`);
                }
            }
        }

        // Attempt rename detection if enabled
        const renameDetectionEnabled = this.configService.getSetting<boolean>('semanticAnalysis.enableRenameDetection');
        this.loggerService.info(`[DIFF_SYMBOLS] 🔄 Rename detection enabled: ${renameDetectionEnabled}`);
        if (renameDetectionEnabled) {
            this.loggerService.info(`[DIFF_SYMBOLS] 🔄 Running rename detection...`);
            this.detectRenames(primitives);
        }

        this.loggerService.info(`[DIFF_SYMBOLS] 🎯 Generated ${primitives.length} primitives:`);
        primitives.forEach((primitive, index) => {
            this.loggerService.info(`  ${index + 1}. ${primitive.operation.toUpperCase()} ${primitive.elementType}: '${primitive.elementName}'${primitive.signature ? ` (${primitive.signature})` : ''}`);
        });

        this.loggerService.info(`[DIFF_SYMBOLS] ✅ Symbol tree diffing completed`);
        return primitives;
    }

    /**
     * Flatten a symbol tree into a map for easier comparison.
     * @param symbols The symbols to flatten.
     * @param map The map to populate.
     * @param containerPath The path of the container.
     * @param currentDepth The current nesting depth.
     * @param maxDepth The maximum nesting depth to process.
     */
    private flattenSymbolTree(
        symbols: vscode.DocumentSymbol[],
        map: Map<string, vscode.DocumentSymbol>,
        containerPath: string,
        currentDepth: number,
        maxDepth: number
    ): void {
        if (currentDepth >= maxDepth) {
            return;
        }

        for (const symbol of symbols) {
            const path = containerPath ? `${containerPath}.${symbol.name}` : symbol.name;
            const key = `${path}:${symbol.kind}`;
            map.set(key, symbol);

            if (symbol.children && symbol.children.length > 0) {
                this.flattenSymbolTree(symbol.children, map, path, currentDepth + 1, maxDepth);
            }
        }
    }

    /**
     * Check if a symbol has changed.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @returns True if the symbol has changed, false otherwise.
     */
    private hasSymbolChanged(
        previousSymbol: vscode.DocumentSymbol,
        currentSymbol: vscode.DocumentSymbol
    ): boolean {
        this.loggerService.info(`[SYMBOL_CHANGE] 🔍 Comparing symbol '${previousSymbol.name}':`);
        this.loggerService.info(`[SYMBOL_CHANGE]   Previous detail: '${previousSymbol.detail || 'none'}'`);
        this.loggerService.info(`[SYMBOL_CHANGE]   Current detail:  '${currentSymbol.detail || 'none'}'`);
        this.loggerService.info(`[SYMBOL_CHANGE]   Previous kind: ${previousSymbol.kind}, Current kind: ${currentSymbol.kind}`);

        // Check if the detail (signature) has changed
        if (previousSymbol.detail !== currentSymbol.detail) {
            this.loggerService.info(`[SYMBOL_CHANGE] ✅ DETAIL CHANGED: '${previousSymbol.detail || 'none'}' → '${currentSymbol.detail || 'none'}'`);
            return true;
        }

        // Check if the kind has changed
        if (previousSymbol.kind !== currentSymbol.kind) {
            this.loggerService.info(`[SYMBOL_CHANGE] ✅ KIND CHANGED: ${previousSymbol.kind} → ${currentSymbol.kind}`);
            return true;
        }

        // Check if the number of children has changed (for classes, modules, etc.)
        const previousChildCount = previousSymbol.children?.length || 0;
        const currentChildCount = currentSymbol.children?.length || 0;
        if (previousChildCount !== currentChildCount) {
            this.loggerService.info(`[SYMBOL_CHANGE] ✅ CHILD COUNT CHANGED: ${previousChildCount} → ${currentChildCount}`);
            return true;
        }

        // Check if the range has changed significantly (more than 2 lines difference)
        const previousRange = previousSymbol.range;
        const currentRange = currentSymbol.range;
        const lineDiff = Math.abs(
            (previousRange.end.line - previousRange.start.line) -
            (currentRange.end.line - currentRange.start.line)
        );

        if (lineDiff > 2) {
            this.loggerService.info(`[SYMBOL_CHANGE] ✅ RANGE CHANGED SIGNIFICANTLY: line diff = ${lineDiff}`);
            return true;
        }

        this.loggerService.info(`[SYMBOL_CHANGE] ❌ NO CHANGE DETECTED for '${previousSymbol.name}'`);
        return false;
    }

    /**
     * Create a semantic primitive from a document symbol.
     * @param symbol The document symbol.
     * @param operation The type of operation.
     * @param symbolPath The full path of the symbol.
     * @returns A semantic primitive.
     */
    private createPrimitiveFromSymbol(
        symbol: vscode.DocumentSymbol,
        operation: 'add' | 'remove' | 'modify' | 'rename',
        symbolPath: string
    ): SemanticPrimitive {
        const parentPath = symbolPath.includes('.') ? symbolPath.substring(0, symbolPath.lastIndexOf('.')) : undefined;

        return {
            operation,
            elementType: this.getElementTypeFromSymbolKind(symbol.kind),
            elementName: symbol.name,
            signature: symbol.detail,
            range: {
                startLine: symbol.range.start.line,
                startCharacter: symbol.range.start.character,
                endLine: symbol.range.end.line,
                endCharacter: symbol.range.end.character
            },
            parentElement: parentPath ? {
                name: parentPath.split('.').pop() || '',
                type: 'unknown' // We'd need to track parent types for this
            } : undefined
        };
    }

    /**
     * Create a modified semantic primitive from two document symbols.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @param symbolPath The full path of the symbol.
     * @returns A semantic primitive.
     */
    private createModifiedPrimitive(
        previousSymbol: vscode.DocumentSymbol,
        currentSymbol: vscode.DocumentSymbol,
        symbolPath: string
    ): SemanticPrimitive {
        const parentPath = symbolPath.includes('.') ? symbolPath.substring(0, symbolPath.lastIndexOf('.')) : undefined;

        return {
            operation: 'modify',
            elementType: this.getElementTypeFromSymbolKind(currentSymbol.kind),
            elementName: currentSymbol.name,
            signature: currentSymbol.detail,
            range: {
                startLine: currentSymbol.range.start.line,
                startCharacter: currentSymbol.range.start.character,
                endLine: currentSymbol.range.end.line,
                endCharacter: currentSymbol.range.end.character
            },
            parentElement: parentPath ? {
                name: parentPath.split('.').pop() || '',
                type: 'unknown'
            } : undefined
        };
    }

    /**
     * Get the semantic primitive element type from a symbol kind.
     * @param kind The symbol kind.
     * @returns The semantic primitive element type.
     */
    private getElementTypeFromSymbolKind(kind: vscode.SymbolKind): SemanticPrimitive['elementType'] {
        switch (kind) {
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Constructor:
                return 'function';
            case vscode.SymbolKind.Class:
                return 'class';
            case vscode.SymbolKind.Method:
                return 'method';
            case vscode.SymbolKind.Property:
            case vscode.SymbolKind.Field:
                return 'property';
            case vscode.SymbolKind.Interface:
                return 'interface';
            case vscode.SymbolKind.Enum:
                return 'enum';
            case vscode.SymbolKind.Variable:
            case vscode.SymbolKind.Constant:
                return 'variable';
            default:
                return 'unknown';
        }
    }

    /**
     * Calculate confidence score for the analysis.
     * @param primitives The extracted primitives.
     * @param currentSymbols The current symbols.
     * @returns A confidence score between 0 and 1.
     */
    private calculateConfidence(primitives: SemanticPrimitive[], currentSymbols: vscode.DocumentSymbol[]): number {
        // Simple confidence calculation based on symbol extraction success
        if (currentSymbols.length === 0) {
            return primitives.length === 0 ? 1.0 : 0.5;
        }

        // Higher confidence if we found symbols and changes
        return primitives.length > 0 ? 0.9 : 0.8;
    }

    /**
     * Calculate summary of changes.
     * @param primitives The extracted primitives.
     * @returns A summary of the changes.
     */
    private calculateSummary(primitives: SemanticPrimitive[]) {
        const summary = {
            added: 0,
            removed: 0,
            modified: 0,
            renamed: 0
        };

        for (const primitive of primitives) {
            switch (primitive.operation) {
                case 'add':
                    summary.added++;
                    break;
                case 'remove':
                    summary.removed++;
                    break;
                case 'modify':
                    summary.modified++;
                    break;
                case 'rename':
                    summary.renamed++;
                    break;
            }
        }

        return summary;
    }

    /**
     * Detect potential renames in the primitives list.
     * This is a simple heuristic-based approach.
     * @param primitives The primitives to analyze for renames.
     */
    private detectRenames(primitives: SemanticPrimitive[]): void {
        // Simple rename detection: look for add/remove pairs of same type with similar signatures
        const addedPrimitives = primitives.filter(p => p.operation === 'add');
        const removedPrimitives = primitives.filter(p => p.operation === 'remove');

        for (const added of addedPrimitives) {
            for (const removed of removedPrimitives) {
                if (added.elementType === removed.elementType &&
                    added.signature === removed.signature &&
                    added.elementName !== removed.elementName) {

                    // Convert to rename operation
                    added.operation = 'rename';
                    added.oldElementName = removed.elementName;

                    // Remove the removed primitive as it's now part of the rename
                    const removedIndex = primitives.indexOf(removed);
                    if (removedIndex > -1) {
                        primitives.splice(removedIndex, 1);
                    }
                    break;
                }
            }
        }
    }
}
