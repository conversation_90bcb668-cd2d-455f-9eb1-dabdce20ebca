# Contributing to Learning Docs

Thank you for your interest in contributing to Learning Docs! This document provides guidelines and instructions for contributing to this project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

This project and everyone participating in it is governed by our Code of Conduct. By participating, you are expected to uphold this code. Please report unacceptable behavior to the project maintainers.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)
- VS Code (v1.60 or later)

### Setup

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR-USERNAME/one_one.git
   cd one_one/learning-docs
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Build the extension:
   ```bash
   npm run compile
   ```

5. Run the extension:
   - Press F5 in VS Code to launch a new window with the extension loaded
   - Alternatively, run `code --new-window --extensionDevelopmentPath="$(pwd)"`

## Development Workflow

1. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes
3. Compile the extension:
   ```bash
   npm run compile
   ```

4. Test your changes by running the extension (press F5 in VS Code)
5. Commit your changes:
   ```bash
   git commit -m "Add your meaningful commit message here"
   ```

6. Push to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

7. Create a Pull Request from your fork to the main repository

## Pull Request Process

1. Ensure your code follows the coding standards
2. Update the README.md with details of changes if appropriate
3. Update the CHANGELOG.md with details of changes
4. The PR should work in VS Code without errors
5. Include tests for new functionality
6. Include appropriate documentation

## Coding Standards

### TypeScript

- Use TypeScript for all new code
- Follow the existing code style
- Use interfaces for type definitions
- Use async/await for asynchronous code
- Use meaningful variable and function names

### Code Organization

- Keep files focused on a single responsibility
- Use the existing directory structure:
  - `src/services/`: Core services
  - `src/interfaces/`: TypeScript interfaces
  - `src/ui/`: User interface components
  - `src/extension.ts`: Extension entry point

### Naming Conventions

- Use PascalCase for class names, interfaces, and type aliases
- Use camelCase for variables, functions, and method names
- Use UPPER_CASE for constants
- Prefix interfaces with `I` (e.g., `ILoggerService`)
- Prefix private class members with `_` (e.g., `private _disposables: vscode.Disposable[] = []`)

## Testing

- Write unit tests for new functionality
- Ensure all tests pass before submitting a PR
- Run tests with:
  ```bash
  npm test
  ```

## Documentation

- Document all public APIs, classes, interfaces, and methods
- Use JSDoc comments for documentation
- Keep documentation up-to-date with code changes
- Update the README.md if you add new features or change existing ones

### Documentation Style

- Use clear, concise language
- Include examples where appropriate
- Document parameters and return values
- Document exceptions and edge cases

## Architecture

When contributing to Learning Docs, it's important to understand the overall architecture:

### Core Services

- **LoggerService**: Handles logging throughout the extension
- **ConfigService**: Manages configuration settings
- **WorkspaceService**: Provides workspace-related utilities
- **StorageService**: Handles storage and retrieval of documentation records

### Change Detection

- **ChangeEventAggregatorService**: Listens for file changes and emits stable change events
- **ChangeProcessorService**: Processes change events and orchestrates the analysis pipeline

### Analysis Services

- **FileHistoryService**: Retrieves previous versions of files
- **DiffService**: Generates diffs between versions
- **SemanticAnalyzerService**: Analyzes code at a structural level
- **ContextAnalyzerService**: Gathers contextual information about changes
- **AIDocumentationService**: Generates AI-powered documentation

### UI Components

- **DocumentationPanel**: WebView panel for displaying documentation
- **DocumentationTreeProvider**: TreeView for navigating change history
- **StatusBarItem**: Status bar item for quick access

## Thank You

Thank you for contributing to Learning Docs! Your efforts help make this extension better for everyone.
