import * as vscode from 'vscode';

/**
 * Interface for workspace service.
 */
export interface IWorkspaceService {
    /**
     * Get the root URI of the workspace.
     * @returns The workspace root URI, or undefined if not in a workspace.
     */
    getWorkspaceRoot(): vscode.Uri | undefined;
    
    /**
     * Get the path relative to the workspace root.
     * @param uri The URI to convert to a relative path.
     * @returns The relative path, or undefined if not in a workspace.
     */
    getRelativePath(uri: vscode.Uri): string | undefined;
    
    /**
     * Check if a path should be ignored based on configuration.
     * @param filePath The path to check.
     * @returns True if the path should be ignored, false otherwise.
     */
    isPathIgnored(filePath: string): boolean;
}
