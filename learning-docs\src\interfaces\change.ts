/**
 * Represents a detected change in a file.
 */
export interface ChangeDescriptor {
    /**
     * Unique identifier for the change.
     */
    id: string;

    /**
     * Path to the file that was changed.
     */
    filePath: string;

    /**
     * Timestamp when the change was detected.
     */
    timestamp: string;

    /**
     * Type of change event.
     */
    eventType: 'create' | 'modify' | 'delete' | 'baseline';

    /**
     * Previous content of the file (for 'modify' events).
     */
    previousContent?: string;

    /**
     * Current content of the file (for 'create' and 'modify' events).
     */
    currentContent?: string;
}
