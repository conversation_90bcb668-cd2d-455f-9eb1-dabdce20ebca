import * as diff from 'diff';
import { IDiffService } from '../interfaces/diff';
import { ILoggerService } from '../interfaces/logger';

/**
 * Implementation of the diff service.
 */
export class DiffService implements IDiffService {
    constructor(
        private readonly loggerService: ILoggerService
    ) {}
    
    /**
     * Generate a unified diff between two versions of a file.
     * 
     * @param previousContent The previous content of the file.
     * @param currentContent The current content of the file.
     * @param oldFilePath The path to use in the diff for the old file.
     * @param newFilePath The path to use in the diff for the new file.
     * @returns A promise that resolves to the unified diff.
     */
    public async generateUnifiedDiff(
        previousContent: string,
        currentContent: string,
        oldFilePath: string,
        newFilePath: string
    ): Promise<string> {
        try {
            // Use jsdiff to create a unified patch
            const unifiedDiff = diff.createPatch(
                newFilePath,
                previousContent || '',
                currentContent || '',
                `Previous: ${oldFilePath}`,
                `Current: ${newFilePath}`
            );
            
            this.loggerService.info(`Generated diff for: ${newFilePath}`);
            return unifiedDiff;
        } catch (error) {
            this.loggerService.error(`Failed to generate diff: ${error}`);
            throw error;
        }
    }
}
