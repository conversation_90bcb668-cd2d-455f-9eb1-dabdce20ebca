# Semantic Analysis Debug Guide 🔍

## Overview

I've added comprehensive logging throughout the semantic analysis pipeline to help debug why semantic primitives might not be appearing in the tree view. This guide explains how to use the logs to diagnose issues.

## How to View Debug Logs

### 1. **VSCode Output Panel**
1. Open VSCode
2. Go to `View > Output`
3. In the dropdown, select **"Learning Docs"**
4. All extension logs will appear here

### 2. **Developer Console** (Alternative)
1. Open `Help > Toggle Developer Tools`
2. Go to the **Console** tab
3. Look for messages prefixed with `[Learning Docs]`

## Debug Log Categories

### 🔍 **Change Processing Logs**
When a file is saved, you should see:
```
🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for example.ts
  - Has semantic analyzer service: true
  - Semantic analysis enabled: true
  - Event type: modify
  - Has current content: true
✅ STARTING SEMANTIC ANALYSIS for example.ts
📝 Detected language: typescript
📂 File URI: file:///path/to/example.ts
```

### 🚀 **Semantic Analysis Logs**
During semantic analysis:
```
🔍 SEMANTIC ANALYSIS START for /path/to/example.ts
📝 Language: typescript
📄 Current content length: 245
📄 Previous content length: 180
⚙️ Semantic analysis enabled: true
🌐 Language typescript supported: true
🚀 Starting semantic analysis for typescript file: /path/to/example.ts
```

### 📊 **Symbol Extraction Logs**
When extracting symbols from code:
```
📊 Getting current document symbols...
🔍 Getting document symbols for typescript (245 chars)
📝 Creating temporary document with language: typescript
✅ Temporary document created: untitled:Untitled-1
🔍 Executing vscode.executeDocumentSymbolProvider...
📊 LSP returned 2 symbols
  Symbol 1: 5 'TestClass' 
    Child 1: 6 'testMethod' testMethod(): void
✅ Found 2 current symbols
```

### 🎯 **Primitive Generation Logs**
When semantic primitives are found:
```
🔄 Diffing symbol trees...
🎯 Generated 1 semantic primitives
  1. ADD method: 'newMethod' (newMethod(): string)
📈 Analysis confidence: 0.8
📊 Summary: +1 -0 ~0 ↔0
✅ SEMANTIC ANALYSIS COMPLETED: 1 primitives found
```

### 💾 **Storage Logs**
When saving records:
```
💾 SAVING DOCUMENTATION RECORD: abc123-def456
📄 File: example.ts
🔄 Event type: modify
🔧 Semantic primitives: 1
  1. add method: 'newMethod'
✅ SAVED documentation record: abc123-def456 to /path/to/storage/abc123-def456.json
```

### 🌳 **Tree View Logs**
When displaying in tree view:
```
🌳 Getting record items for file: example.ts
📄 Found 1 records for example.ts
  Record 1: modify example.ts
    - Has semantic primitives: true
    - Semantic primitives count: 1
    - Collapsible state: Collapsed
      Primitive 1: add method 'newMethod'
```

## Common Issues & Solutions

### ❌ **Issue 1: Semantic Analysis Not Starting**

**Symptoms:**
```
⏭️ SKIPPING SEMANTIC ANALYSIS for example.ts - conditions not met
```

**Check:**
1. **Extension enabled**: Look for `Semantic analysis enabled: false`
2. **Language support**: Look for `Language [lang] supported: false`
3. **Event type**: Should be `modify`, `create`, or `baseline`
4. **Content**: Should have `Has current content: true`

**Solutions:**
- Enable in settings: `learningDocs.enable.semanticAnalysis: true`
- Add language to: `learningDocs.semanticAnalysis.enabledLanguages`

### ❌ **Issue 2: LSP Not Returning Symbols**

**Symptoms:**
```
📊 LSP returned 0 symbols
⚠️ No symbols returned from LSP
```

**Possible Causes:**
1. **Language Server not running**: TypeScript/JavaScript language server not active
2. **Invalid syntax**: Code has syntax errors
3. **Language not supported**: File extension not recognized

**Solutions:**
- Ensure language server is working: Try "Go to Symbol" (`Ctrl+Shift+O`) in the file
- Check for syntax errors in the file
- Verify file extension matches language ID

### ❌ **Issue 3: No Semantic Primitives Generated**

**Symptoms:**
```
🎯 Generated 0 semantic primitives
```

**Possible Causes:**
1. **No actual changes**: Previous and current symbols are identical
2. **High similarity threshold**: Changes skipped due to text similarity
3. **Symbol extraction failed**: Previous or current symbols empty

**Solutions:**
- Make more significant changes (add/remove functions, classes)
- Lower similarity threshold: `learningDocs.semanticAnalysis.skipIfTextSimilarityAbove`
- Check that both previous and current symbol extraction succeeded

### ❌ **Issue 4: Tree View Not Showing Primitives**

**Symptoms:**
```
📄 Found 1 records for example.ts
  Record 1: modify example.ts
    - Has semantic primitives: false
    - Semantic primitives count: 0
```

**Possible Causes:**
1. **Storage issue**: Primitives not saved to storage
2. **Loading issue**: Primitives not loaded from storage
3. **Tree refresh issue**: Tree view not refreshed after changes

**Solutions:**
- Check storage logs for successful save
- Manually refresh tree view
- Check JSON files in storage directory

## Step-by-Step Debugging Process

### 1. **Test File Creation**
Create a simple TypeScript file:
```typescript
class TestClass {
    existingMethod() {
        return "hello";
    }
}
```

### 2. **Save and Check Logs**
Save the file and look for:
- ✅ Change processing logs
- ✅ Semantic analysis start logs
- ✅ Symbol extraction logs
- ✅ Storage logs

### 3. **Make Changes**
Add a new method:
```typescript
class TestClass {
    existingMethod() {
        return "hello";
    }
    
    newMethod() {
        return "world";
    }
}
```

### 4. **Save and Verify**
Save again and look for:
- ✅ Primitive generation logs
- ✅ Storage logs with primitives
- ✅ Tree view logs showing primitives

### 5. **Check Tree View**
In the Learning Docs tree view:
- File should show as expandable
- Record should show semantic change count
- Expanding should show individual primitives

## Manual Verification

### Check Storage Files
Navigate to storage directory and check JSON files:
```json
{
  "semanticPrimitives": [
    {
      "operation": "add",
      "elementType": "method",
      "elementName": "newMethod",
      "signature": "newMethod(): string"
    }
  ]
}
```

### Check Configuration
Verify settings in VSCode:
```json
{
  "learningDocs.enable.semanticAnalysis": true,
  "learningDocs.semanticAnalysis.enabledLanguages": [
    "typescript", "javascript", "python"
  ]
}
```

## Expected Log Flow

For a successful semantic analysis, you should see this sequence:

1. 🔍 **Change detected** → Conditions checked
2. 🚀 **Analysis starts** → Language and content validated  
3. 📊 **Symbols extracted** → Current and previous symbols found
4. 🎯 **Primitives generated** → Differences identified
5. 💾 **Record saved** → Primitives stored to disk
6. 🌳 **Tree updated** → Primitives displayed in UI

If any step fails, the logs will show exactly where and why!

## Getting Help

If you're still having issues:

1. **Copy the relevant logs** from the Output panel
2. **Check the storage directory** for JSON files
3. **Verify configuration** settings
4. **Test with simple TypeScript files** first

The detailed logging should help identify exactly where the semantic analysis pipeline is failing! 🎯
