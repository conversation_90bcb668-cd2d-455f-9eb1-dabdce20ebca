import * as vscode from 'vscode';
import * as path from 'path';
import { ChangeDocumentationRecord } from '../interfaces/storage';
import { ILoggerService } from '../interfaces/logger';
import { IStorageService } from '../interfaces/storage-service';

/**
 * WebView panel for displaying documentation.
 */
export class DocumentationPanel {
    public static readonly viewType = 'learningDocs.documentationPanel';

    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];

    private constructor(
        panel: vscode.WebviewPanel,
        extensionUri: vscode.Uri,
        private readonly loggerService: ILoggerService,
        private readonly storageService: IStorageService
    ) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        // Set the webview's initial html content
        this._update();

        // Listen for when the panel is disposed
        // This happens when the user closes the panel or when the panel is closed programmatically
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // Update the content based on view changes
        this._panel.onDidChangeViewState(
            e => {
                if (this._panel.visible) {
                    this._update();
                }
            },
            null,
            this._disposables
        );

        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'showRecord':
                        this._showRecord(message.recordId);
                        return;
                }
            },
            null,
            this._disposables
        );
    }

    /**
     * Create or show a documentation panel.
     * @param extensionUri The URI of the extension.
     * @param loggerService The logger service.
     * @param storageService The storage service.
     * @returns The documentation panel.
     */
    public static createOrShow(
        extensionUri: vscode.Uri,
        loggerService: ILoggerService,
        storageService: IStorageService
    ): DocumentationPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // If we already have a panel, show it
        if (DocumentationPanel.currentPanel) {
            DocumentationPanel.currentPanel._panel.reveal(column);
            return DocumentationPanel.currentPanel;
        }

        // Otherwise, create a new panel
        const panel = vscode.window.createWebviewPanel(
            DocumentationPanel.viewType,
            'Learning Documentation',
            column || vscode.ViewColumn.One,
            {
                // Enable JavaScript in the webview
                enableScripts: true,

                // Restrict the webview to only load resources from the extension
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media')
                ],

                // Retain context when hidden
                retainContextWhenHidden: true
            }
        );

        DocumentationPanel.currentPanel = new DocumentationPanel(panel, extensionUri, loggerService, storageService);
        return DocumentationPanel.currentPanel;
    }

    /**
     * Show documentation for a specific file.
     * @param filePath The path to the file.
     */
    public async showDocumentationForFile(filePath: string): Promise<void> {
        try {
            this.loggerService.info(`Showing documentation for file: ${filePath}`);

            // Get records for the file
            const records = await this.storageService.getRecordsByFile(filePath);

            if (records.length === 0) {
                this._panel.webview.html = this._getNoDocumentationHtml(filePath);
                return;
            }

            // Show the most recent record
            await this._showRecord(records[0].id);
        } catch (error) {
            this.loggerService.error(`Error showing documentation for file: ${error}`);
            this._panel.webview.html = this._getErrorHtml(`Failed to load documentation for ${filePath}`);
        }
    }

    /**
     * Show documentation for a specific record.
     * @param recordId The ID of the record to show.
     */
    public async showDocumentationForRecord(recordId: string): Promise<void> {
        try {
            this.loggerService.info(`Showing documentation for record: ${recordId}`);
            await this._showRecord(recordId);
        } catch (error) {
            this.loggerService.error(`Error showing documentation for record: ${error}`);
            this._panel.webview.html = this._getErrorHtml(`Failed to load record ${recordId}`);
        }
    }

    /**
     * Show a specific documentation record.
     * @param recordId The ID of the record to show.
     */
    private async _showRecord(recordId: string): Promise<void> {
        try {
            const record = await this.storageService.getRecordById(recordId);

            if (!record) {
                this._panel.webview.html = this._getErrorHtml(`Record not found: ${recordId}`);
                return;
            }

            this._panel.webview.html = this._getDocumentationHtml(record);
        } catch (error) {
            this.loggerService.error(`Error showing record: ${error}`);
            this._panel.webview.html = this._getErrorHtml(`Failed to load record ${recordId}`);
        }
    }

    /**
     * Update the webview content.
     */
    private _update(): void {
        this._panel.title = 'Learning Documentation';
        this._panel.webview.html = this._getWelcomeHtml();
    }

    /**
     * Get the HTML for the welcome page.
     * @returns The HTML for the welcome page.
     */
    private _getWelcomeHtml(): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Learning Documentation</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }
                h1 {
                    color: var(--vscode-editor-foreground);
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Learning Documentation</h1>
                <p>Select a file to view its documentation.</p>
            </div>
        </body>
        </html>`;
    }

    /**
     * Get the HTML for a documentation record.
     * @param record The documentation record.
     * @returns The HTML for the documentation record.
     */
    private _getDocumentationHtml(record: ChangeDocumentationRecord): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Documentation for ${record.filePath}</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: var(--vscode-editor-foreground);
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .section {
                    margin-bottom: 20px;
                    padding: 10px;
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 5px;
                }
                pre {
                    background-color: var(--vscode-textCodeBlock-background);
                    padding: 10px;
                    border-radius: 5px;
                    overflow: auto;
                    font-family: var(--vscode-editor-font-family);
                    font-size: var(--vscode-editor-font-size);
                }
                .diff-added {
                    background-color: rgba(0, 255, 0, 0.1);
                }
                .diff-removed {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .primitive {
                    margin-bottom: 10px;
                    padding: 5px;
                    border-left: 3px solid var(--vscode-activityBarBadge-background);
                }
                .primitive-add {
                    border-left-color: green;
                }
                .primitive-remove {
                    border-left-color: red;
                }
                .primitive-modify {
                    border-left-color: orange;
                }
                .primitive-rename {
                    border-left-color: blue;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Documentation for ${record.filePath}</h1>
                <p><strong>Change Type:</strong> ${record.eventType}</p>
                <p><strong>Timestamp:</strong> ${record.timestamp}</p>

                ${record.aiAnalysis ? this._getAIAnalysisHtml(record) : ''}

                ${record.semanticPrimitives && record.semanticPrimitives.length > 0 ?
                    this._getSemanticPrimitivesHtml(record) : ''}

                ${record.rawDiff ? this._getDiffHtml(record) : ''}

                ${record.contextualInfo ? this._getContextualInfoHtml(record) : ''}
            </div>
        </body>
        </html>`;
    }

    /**
     * Get the HTML for the AI analysis section.
     * @param record The documentation record.
     * @returns The HTML for the AI analysis section.
     */
    private _getAIAnalysisHtml(record: ChangeDocumentationRecord): string {
        if (!record.aiAnalysis) {
            return '';
        }

        const { summary, explanation, impact, suggestions, learningResources, codeExamples } = record.aiAnalysis;

        return `
        <div class="section">
            <h2>AI Analysis</h2>
            <h3>Summary</h3>
            <p>${summary}</p>

            <h3>Explanation</h3>
            <p>${explanation}</p>

            ${impact ? `<h3>Impact</h3><p>${impact}</p>` : ''}

            ${suggestions && suggestions.length > 0 ? `
                <h3>Suggestions</h3>
                <ul>
                    ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>
            ` : ''}

            ${learningResources && learningResources.length > 0 ? `
                <h3>Learning Resources</h3>
                <ul>
                    ${learningResources.map(resource => `
                        <li>
                            <strong>${resource.title}</strong>
                            ${resource.url ? `<a href="${resource.url}">${resource.url}</a>` : ''}
                            <p>${resource.description}</p>
                        </li>
                    `).join('')}
                </ul>
            ` : ''}

            ${codeExamples && codeExamples.length > 0 ? `
                <h3>Code Examples</h3>
                ${codeExamples.map(example => `
                    <div>
                        <h4>${example.title}</h4>
                        <pre><code>${this._escapeHtml(example.code)}</code></pre>
                        <p>${example.explanation}</p>
                    </div>
                `).join('')}
            ` : ''}
        </div>`;
    }

    /**
     * Get the HTML for the semantic primitives section.
     * @param record The documentation record.
     * @returns The HTML for the semantic primitives section.
     */
    private _getSemanticPrimitivesHtml(record: ChangeDocumentationRecord): string {
        if (!record.semanticPrimitives || record.semanticPrimitives.length === 0) {
            return '';
        }

        return `
        <div class="section">
            <h2>Semantic Changes</h2>
            ${record.semanticPrimitives.map(primitive => `
                <div class="primitive primitive-${primitive.operation}">
                    <strong>${primitive.elementType} ${primitive.elementName}</strong> (${primitive.operation})
                    ${primitive.signature ? `<p>Signature: ${this._escapeHtml(primitive.signature)}</p>` : ''}
                    ${primitive.oldElementName ? `<p>Previous Name: ${primitive.oldElementName}</p>` : ''}
                    ${primitive.parentElement ? `<p>Parent: ${primitive.parentElement.type} ${primitive.parentElement.name}</p>` : ''}
                    ${primitive.range ? `<p>Location: Line ${primitive.range.startLine + 1}-${primitive.range.endLine + 1}</p>` : ''}
                </div>
            `).join('')}
        </div>`;
    }

    /**
     * Get the HTML for the diff section.
     * @param record The documentation record.
     * @returns The HTML for the diff section.
     */
    private _getDiffHtml(record: ChangeDocumentationRecord): string {
        if (!record.rawDiff) {
            return '';
        }

        // Process the diff to add syntax highlighting
        const processedDiff = record.rawDiff
            .split('\n')
            .map(line => {
                if (line.startsWith('+')) {
                    return `<div class="diff-added">${this._escapeHtml(line)}</div>`;
                } else if (line.startsWith('-')) {
                    return `<div class="diff-removed">${this._escapeHtml(line)}</div>`;
                } else {
                    return this._escapeHtml(line);
                }
            })
            .join('\n');

        return `
        <div class="section">
            <h2>Diff</h2>
            <pre><code>${processedDiff}</code></pre>
        </div>`;
    }

    /**
     * Get the HTML for the contextual information section.
     * @param record The documentation record.
     * @returns The HTML for the contextual information section.
     */
    private _getContextualInfoHtml(record: ChangeDocumentationRecord): string {
        if (!record.contextualInfo) {
            return '';
        }

        const { relatedFiles, references, relatedImports, relatedDependencies, commitHistory, relatedTests } = record.contextualInfo;

        return `
        <div class="section">
            <h2>Contextual Information</h2>

            ${relatedFiles && relatedFiles.length > 0 ? `
                <h3>Related Files</h3>
                <ul>
                    ${relatedFiles.map(file => `<li>${file}</li>`).join('')}
                </ul>
            ` : ''}

            ${references && references.length > 0 ? `
                <h3>References</h3>
                <ul>
                    ${references.map(ref => `
                        <li>
                            <strong>${ref.filePath}:${ref.lineNumber}</strong>
                            <pre><code>${this._escapeHtml(ref.context)}</code></pre>
                        </li>
                    `).join('')}
                </ul>
            ` : ''}

            ${relatedImports && relatedImports.length > 0 ? `
                <h3>Related Imports</h3>
                <ul>
                    ${relatedImports.map(imp => `<li>${imp}</li>`).join('')}
                </ul>
            ` : ''}

            ${relatedDependencies && relatedDependencies.length > 0 ? `
                <h3>Related Dependencies</h3>
                <ul>
                    ${relatedDependencies.map(dep => `<li>${dep}</li>`).join('')}
                </ul>
            ` : ''}

            ${commitHistory && commitHistory.length > 0 ? `
                <h3>Commit History</h3>
                <ul>
                    ${commitHistory.map(commit => `
                        <li>
                            <strong>${commit.commitHash.substring(0, 7)}</strong>
                            <p>${commit.message} (${commit.author}, ${commit.date})</p>
                        </li>
                    `).join('')}
                </ul>
            ` : ''}

            ${relatedTests && relatedTests.length > 0 ? `
                <h3>Related Tests</h3>
                <ul>
                    ${relatedTests.map(test => `<li>${test}</li>`).join('')}
                </ul>
            ` : ''}
        </div>`;
    }

    /**
     * Get the HTML for the no documentation page.
     * @param filePath The path to the file.
     * @returns The HTML for the no documentation page.
     */
    private _getNoDocumentationHtml(filePath: string): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>No Documentation</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }
                h1 {
                    color: var(--vscode-editor-foreground);
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>No Documentation Available</h1>
                <p>No documentation records found for ${filePath}.</p>
                <p>Make changes to the file to generate documentation.</p>
            </div>
        </body>
        </html>`;
    }

    /**
     * Get the HTML for the error page.
     * @param errorMessage The error message.
     * @returns The HTML for the error page.
     */
    private _getErrorHtml(errorMessage: string): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }
                h1 {
                    color: var(--vscode-editor-foreground);
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .error {
                    color: var(--vscode-errorForeground);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Error</h1>
                <p class="error">${errorMessage}</p>
            </div>
        </body>
        </html>`;
    }

    /**
     * Escape HTML special characters.
     * @param text The text to escape.
     * @returns The escaped text.
     */
    private _escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    /**
     * Dispose of the panel.
     */
    public dispose(): void {
        DocumentationPanel.currentPanel = undefined;

        // Clean up our resources
        this._panel.dispose();

        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }

    private static currentPanel: DocumentationPanel | undefined;
}
