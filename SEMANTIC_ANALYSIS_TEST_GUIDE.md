# Semantic Analysis Testing Guide 🧪

## Overview

This guide provides step-by-step instructions for testing the enhanced semantic analysis functionality. The improvements include:

1. **Fixed Directory Processing Error** - No more errors when creating directories
2. **Enhanced Symbol Change Detection** - Better detection of modifications
3. **Comprehensive Logging** - Detailed debug information for troubleshooting

## Prerequisites

1. Ensure the extension is compiled: `npm run compile`
2. Reload VS Code window: `Ctrl+Shift+P` → "Developer: Reload Window"
3. Open the `test_semantic.py` file created in the workspace

## Test Cases

### **Test Case 1: Directory Creation (Priority 1 Fix)**

**Objective**: Verify that creating directories no longer causes errors.

**Steps**:
1. Create a new directory: Right-click in Explorer → "New Folder" → Name it `test_directory`
2. Create a subdirectory: Right-click on `test_directory` → "New Folder" → Name it `subdirectory`
3. Check the Output panel (View → Output → Select "Learning Docs")

**Expected Result**:
- Should see logs like: `Directory created: test_directory (skipping content analysis)`
- Should see logs like: `Directory created: test_directory/subdirectory (skipping content analysis)`
- **No errors** about "cannot open file... that is actually a directory"

### **Test Case 2: Function Modification Detection**

**Objective**: Test that function signature changes are detected as 'modify' operations.

**Setup**: Ensure `test_semantic.py` is saved and processed (check logs for baseline creation)

**Steps**:
1. Open `test_semantic.py`
2. Modify the `add` method signature:
   ```python
   # Change from:
   def add(self, x: int, y: int) -> int:
   
   # Change to:
   def add(self, x: int, y: int, z: int = 0) -> int:
   ```
3. Save the file (`Ctrl+S`)
4. Check the Output panel for semantic analysis logs

**Expected Result**:
- Should see `[SYMBOL_CHANGE] ✅ DETAIL CHANGED` for the `add` method
- Should generate a semantic primitive with `operation: 'modify'`, `elementType: 'method'`, `elementName: 'add'`

### **Test Case 3: Function Removal Detection**

**Objective**: Test that deleted functions are detected as 'remove' operations.

**Steps**:
1. Delete the entire `divide` function from `test_semantic.py`
2. Save the file
3. Check the logs

**Expected Result**:
- Should see `[DIFF_SYMBOLS] ✅ FOUND REMOVE` for the `divide` function
- Should generate a semantic primitive with `operation: 'remove'`, `elementType: 'function'`, `elementName: 'divide'`

### **Test Case 4: Function Addition Detection**

**Objective**: Test that new functions are detected as 'add' operations.

**Steps**:
1. Add a new function to `test_semantic.py`:
   ```python
   def power(base: float, exponent: float) -> float:
       """Calculate base raised to the power of exponent."""
       return base ** exponent
   ```
2. Save the file
3. Check the logs

**Expected Result**:
- Should see `[DIFF_SYMBOLS] ✅ FOUND ADD` for the `power` function
- Should generate a semantic primitive with `operation: 'add'`, `elementType: 'function'`, `elementName: 'power'`

### **Test Case 5: Class Method Addition**

**Objective**: Test that adding methods to existing classes is detected correctly.

**Steps**:
1. Add a new method to the `Calculator` class:
   ```python
   def reset(self) -> None:
       """Reset the calculator result to zero."""
       self.result = 0
   ```
2. Save the file
3. Check the logs

**Expected Result**:
- Should see `[DIFF_SYMBOLS] ✅ FOUND ADD` for the `reset` method
- Should generate a semantic primitive with `operation: 'add'`, `elementType: 'method'`, `elementName: 'reset'`
- May also see a `modify` operation for the `Calculator` class due to child count change

### **Test Case 6: No Semantic Change (Internal Logic)**

**Objective**: Verify that internal function changes without signature changes don't generate primitives.

**Steps**:
1. Modify only the body of the `multiply` function:
   ```python
   def multiply(a: int, b: int) -> int:
       """Multiply two numbers."""
       # Add a comment and change implementation
       result = a * b  # Store in variable first
       return result
   ```
2. Save the file
3. Check the logs

**Expected Result**:
- Should see `[SYMBOL_CHANGE] ❌ NO CHANGE DETECTED for 'multiply'`
- Should **not** generate any semantic primitives for the `multiply` function
- Raw diff should still be generated showing the content changes

## Debugging Tips

### Key Log Patterns to Look For:

1. **Directory Processing**: Look for `(skipping content analysis)` messages
2. **Symbol Extraction**: Look for `📊 LSP returned X symbols` messages
3. **Symbol Comparison**: Look for `[DIFF_SYMBOLS]` and `[SYMBOL_CHANGE]` prefixed logs
4. **Primitive Generation**: Look for `🎯 Generated X semantic primitives` messages

### Common Issues:

1. **No symbols detected**: Check if the language server is running for Python
2. **No semantic analysis**: Verify the file extension is supported and semantic analysis is enabled
3. **Missing previous content**: Ensure the file was previously saved and cached

## UI Verification

After testing the semantic changes:

1. **TreeView**: Check the Learning Documentation panel in Explorer
   - Should show change records with semantic primitive counts
   - Should display collapsible items for each primitive

2. **Documentation Panel**: Click on a change record
   - Should display the semantic primitives in a readable format
   - Should show operation types, element names, and signatures

## Success Criteria

✅ **Priority 1**: No directory processing errors
✅ **Priority 2**: Accurate detection of modify, remove, and add operations
✅ **Priority 3**: UI properly displays semantic information

The semantic analysis is working correctly when all test cases pass and the logs show detailed, accurate information about the detected changes.
